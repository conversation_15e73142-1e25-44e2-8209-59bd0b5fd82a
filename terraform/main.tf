terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

resource "google_workflows_workflow" "discord-notification" {
  name            = "discord-notification"
  description     = "Discord notification workflow"
  project         = var.project_id
  region          = var.region
  service_account = "medeze-data-jobs-sa@${var.project_id}.iam.gserviceaccount.com"
  source_contents = file("template/discord.yaml")
}
# Cloud Run Job for data processing
module "quant-price-performance" {
  source = "./modules/scheduler-workflow"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {
    "jobs-quant-price-performance" = {
      python_file_path = "jobs/quant/price_performance.py"
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    }

  }
  workflow_name        = "wf-quant-price-performance"
  workflow_description = "Jobs fetch data from set for quant price performance"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 7 * * *"
}

module "apify-facebook" {
  source = "./modules/scheduler-workflow"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {
    "jobs-facebook-post-ingest" = {
      python_file_path = "jobs/scrape/apify/ingest.py"
      python_args      = ["facebook-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-facebook-post-transform" = {
      python_file_path = "jobs/scrape/apify/transform.py"
      python_args      = ["facebook-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-facebook-comment-ingest" = {
      python_file_path = "jobs/scrape/apify/ingest.py"
      python_args      = ["facebook-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-facebook-comment-transform" = {
      python_file_path = "jobs/scrape/apify/transform.py"
      python_args      = ["facebook-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-facebook-post-sentiment" = {
      python_file_path = "jobs/content_sentiment.py"
      python_args      = ["facebook-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-facebook-comment-sentiment" = {
      python_file_path = "jobs/content_sentiment.py"
      python_args      = ["facebook-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    }
  }
  job_order = [
    "jobs-facebook-post-ingest",
    "jobs-facebook-post-transform",
    "jobs-facebook-comment-ingest",
    "jobs-facebook-comment-transform",
    "jobs-facebook-post-sentiment",
    "jobs-facebook-comment-sentiment"
  ]
  workflow_name        = "wf-apify-facebook"
  workflow_description = "Jobs scrape data from facebook with apify"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 7 * * *"
}

module "apify-instagram" {
  source = "./modules/scheduler-workflow"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {
    "jobs-instagram-post-ingest" = {
      python_file_path = "jobs/scrape/apify/ingest.py"
      python_args      = ["instagram-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-instagram-post-transform" = {
      python_file_path = "jobs/scrape/apify/transform.py"
      python_args      = ["instagram-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-instagram-comment-ingest" = {
      python_file_path = "jobs/scrape/apify/ingest.py"
      python_args      = ["instagram-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-instagram-comment-transform" = {
      python_file_path = "jobs/scrape/apify/transform.py"
      python_args      = ["instagram-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-instagram-post-sentiment" = {
      python_file_path = "jobs/content_sentiment.py"
      python_args      = ["instagram-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-instagram-comment-sentiment" = {
      python_file_path = "jobs/content_sentiment.py"
      python_args      = ["instagram-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    }
  }
  job_order = [
    "jobs-instagram-post-ingest",
    "jobs-instagram-post-transform",
    "jobs-instagram-comment-ingest",
    "jobs-instagram-comment-transform",
    "jobs-instagram-post-sentiment",
    "jobs-instagram-comment-sentiment"
  ]

  workflow_name        = "wf-apify-instagram"
  workflow_description = "Jobs scrape data from instagram with apify"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 7 * * *"
}

module "apify-tiktok" {
  source = "./modules/scheduler-workflow"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {
    "jobs-tiktok-post-ingest" = {
      python_file_path = "jobs/scrape/apify/ingest.py"
      python_args      = ["tiktok-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-tiktok-post-transform" = {
      python_file_path = "jobs/scrape/apify/transform.py"
      python_args      = ["tiktok-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-tiktok-comment-ingest" = {
      python_file_path = "jobs/scrape/apify/ingest.py"
      python_args      = ["tiktok-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-tiktok-comment-transform" = {
      python_file_path = "jobs/scrape/apify/transform.py"
      python_args      = ["tiktok-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-tiktok-post-sentiment" = {
      python_file_path = "jobs/content_sentiment.py"
      python_args      = ["tiktok-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-tiktok-comment-sentiment" = {
      python_file_path = "jobs/content_sentiment.py"
      python_args      = ["tiktok-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    }

  }
  job_order = [
    "jobs-tiktok-post-ingest",
    "jobs-tiktok-post-transform",
    "jobs-tiktok-comment-ingest",
    "jobs-tiktok-comment-transform",
    "jobs-tiktok-post-sentiment",
    "jobs-tiktok-comment-sentiment"
  ]
  workflow_name        = "wf-apify-tiktok"
  workflow_description = "Jobs scrape data from tiktok with apify"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 7 * * *"
}

module "apify-youtube" {
  source = "./modules/scheduler-workflow"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {
    "jobs-youtube-post-ingest" = {
      python_file_path = "jobs/scrape/apify/ingest.py"
      python_args      = ["youtube-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-youtube-post-transform" = {
      python_file_path = "jobs/scrape/apify/transform.py"
      python_args      = ["youtube-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-youtube-comment-ingest" = {
      python_file_path = "jobs/scrape/apify/ingest.py"
      python_args      = ["youtube-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-youtube-comment-transform" = {
      python_file_path = "jobs/scrape/apify/transform.py"
      python_args      = ["youtube-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-youtube-post-sentiment" = {
      python_file_path = "jobs/content_sentiment.py"
      python_args      = ["youtube-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-youtube-comment-sentiment" = {
      python_file_path = "jobs/content_sentiment.py"
      python_args      = ["youtube-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    }
  }
  job_order = [
    "jobs-youtube-post-ingest",
    "jobs-youtube-post-transform",
    "jobs-youtube-comment-ingest",
    "jobs-youtube-comment-transform",
    "jobs-youtube-post-sentiment",
    "jobs-youtube-comment-sentiment"
  ]
  workflow_name        = "wf-apify-youtube"
  workflow_description = "Jobs scrape data from youtube with apify"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 7 * * *"
}

module "apify-twitter" {
  source = "./modules/scheduler-workflow"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {
    "jobs-twitter-ingest" = {
      python_file_path = "jobs/scrape/apify/ingest.py"
      python_args      = ["twitter-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-twitter-post-transform" = {
      python_file_path = "jobs/scrape/apify/transform.py"
      python_args      = ["twitter-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-twitter-comment-ingest" = {
      python_file_path = "jobs/scrape/apify/ingest.py"
      python_args      = ["twitter-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-twitter-comment-transform" = {
      python_file_path = "jobs/scrape/apify/transform.py"
      python_args      = ["twitter-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-twitter-post-sentiment" = {
      python_file_path = "jobs/content_sentiment.py"
      python_args      = ["twitter-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-twitter-comment-sentiment" = {
      python_file_path = "jobs/content_sentiment.py"
      python_args      = ["twitter-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    }
  }
  job_order = [
    "jobs-twitter-ingest",
    "jobs-twitter-post-transform",
    "jobs-twitter-comment-ingest",
    "jobs-twitter-comment-transform",
    "jobs-twitter-post-sentiment",
    "jobs-twitter-comment-sentiment"
  ]
  workflow_name        = "wf-apify-twitter"
  workflow_description = "Jobs scrape data from twitter with apify"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 7 * * *"
}

module "thaivi" {
  source = "./modules/scheduler-workflow"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {
    "jobs-thaivi-ingest" = {
      python_file_path = "jobs/scrape/thaivi/thaivi_ingest.py"
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "600s" # min
      max_retries      = 3
    },
    "jobs-thaivi-post-transform" = {
      python_file_path = "jobs/scrape/thaivi/thaivi_post_transform.py"
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-thaivi-comment-transform" = {
      python_file_path = "jobs/scrape/thaivi/thaivi_comment_transform.py"
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-thaivi-post-sentiment" = {
      python_file_path = "jobs/content_sentiment.py"
      python_args      = ["thaivi-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-thaivi-comment-sentiment" = {
      python_file_path = "jobs/content_sentiment.py"
      python_args      = ["thaivi-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    }
  }
  job_order = [
    "jobs-thaivi-ingest",
    "jobs-thaivi-post-transform",
    "jobs-thaivi-comment-transform",
    "jobs-thaivi-post-sentiment",
    "jobs-thaivi-comment-sentiment"
  ]
  workflow_name        = "wf-thaivi"
  workflow_description = "Jobs scrape data from Thaivi webboard"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 7 * * *"
}

module "pantip" {
  source = "./modules/scheduler-workflow"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {
    "jobs-pantip-update" = {
      python_file_path = "jobs/scrape/pantip/update.py"
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "600s" # min
      max_retries      = 3
    },
    "jobs-pantip-search" = {
      python_file_path = "jobs/scrape/pantip/search.py"
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-pantip-post-sentiment" = {
      python_file_path = "jobs/content_sentiment.py"
      python_args      = ["pantip-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-pantip-mention-sentiment" = {
      python_file_path = "jobs/content_sentiment.py"
      python_args      = ["pantip-mention"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-pantip-comment-post-sentiment" = {
      python_file_path = "jobs/content_sentiment.py"
      python_args      = ["pantip-comment-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-pantip-comment-mention-sentiment" = {
      python_file_path = "jobs/content_sentiment.py"
      python_args      = ["pantip-comment-mention"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    }

  }
  job_order = [
    "jobs-pantip-update",
    "jobs-pantip-search",
    "jobs-pantip-post-sentiment",
    "jobs-pantip-mention-sentiment",
    "jobs-pantip-comment-post-sentiment",
    "jobs-pantip-comment-mention-sentiment"
  ]
  workflow_name        = "wf-pantip"
  workflow_description = "Jobs scrape data from Pantip"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 7 * * *"
}

module "news" {
  source = "./modules/scheduler-workflow"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {
    "jobs-news" = {
      python_file_path = "jobs/scrape/news.py"
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "600s" # min
      max_retries      = 3
    },
    "jobs-news-sentiment" = {
      python_file_path = "jobs/content_sentiment.py"
      python_args      = ["news"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    }
  }

  job_order = [
    "jobs-news",
    "jobs-news-sentiment"
  ]
  workflow_name        = "wf-news"
  workflow_description = "Jobs scrape data from Google News"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 7 * * *"
}

module "stock_price" {
  source = "./modules/scheduler-workflow"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {
    "jobs-quant-stock-price" = {
      python_file_path = "jobs/stock_price_job.py"
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "600s" # min
      max_retries      = 3
    }
  }

  workflow_name        = "wf-stock-price-job"
  workflow_description = "Jobs scrape stock price data from TradingView"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 */4 * * *"
}

module "quant_robot" {
  source = "./modules/scheduler-workflow"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {
    "jobs-quant-robot" = {
      python_file_path = "jobs/quant/robot.py"
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "600s" # min
      max_retries      = 3
    }
  }

  workflow_name        = "wf-quant-robot"
  workflow_description = "Jobs capture Medeze price chart and analyze with LLM"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 7 * * *"
}

module "quant_financial_status" {
  source = "./modules/scheduler-workflow"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {

    "jobs-quant-financial-status" = {
      python_file_path = "jobs/quant/financial_status.py"
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "600s" # min
      max_retries      = 3
    }
  }

  workflow_name        = "wf-quant-financial-status"
  workflow_description = "Jobs analyze financial status from topic and sentiment data"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 9 * * *"
}

module "line_openchat" {
  source = "./modules/gcs-workflow"

  project_id           = var.project_id
  region               = var.region
  container_image      = var.container_image
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  source_folder        = "raw/line-openchat/"
  workflow_name        = "wf-line-openchat"
  workflow_description = "Jobs process line openchat data"
  job_name             = "jobs-line-openchat"
  job_python_file_path = "jobs/scrape/line_openchat.py"
  job_cpu              = "1"
  job_memory           = "512Mi"
  job_timeout          = "14400s"
  job_max_retries      = 3
  additional_jobs = {
    "jobs-line-sentiment" = {
      python_file_path = "jobs/line_sentiment.py"
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    }
  }
}


module "quant_investor_relation" {
  source = "./modules/scheduler-workflow"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {

    "jobs-quant-investor-relation" = {
      python_file_path = "jobs/quant/investor_relation.py"
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "600s" # min
      max_retries      = 3
    }
  }

  workflow_name        = "wf-quant-investor-relation"
  workflow_description = "Jobs generate concerns and recommended plan in quant page"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 9 * * *"
}

module "feedback_current_sentiment" {
  source = "./modules/scheduler-workflow"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {

    "jobs-feedback" = {
      python_file_path = "jobs/feedback.py"
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "600s" # min
      max_retries      = 3
    }
    "jobs-current-sentiment" = {
      python_file_path = "jobs/dashboard/current_sentiment.py"
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "600s" # min
      max_retries      = 3
    }
  }

  workflow_name        = "wf-feedback-current-sentiment"
  workflow_description = "Jobs generate analytical feedback and dashbard current sentiment"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 9 * * *"
}
