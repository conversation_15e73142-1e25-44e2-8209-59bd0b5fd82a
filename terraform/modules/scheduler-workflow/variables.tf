variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "region" {
  description = "The region where resources will be created"
  type        = string
  default     = "asia-southeast1"
}

variable "container_image" {
  description = "The container image to use for the Cloud Run Job"
  type        = string
}

variable "jobs" {
  description = "Map of Cloud Run Jobs configurations"
  type = map(object({
    python_file_path = string
    python_args      = optional(list(string), [])
    cpu              = optional(string, "1000m")
    memory           = optional(string, "512Mi")
    timeout          = optional(string, "14400s")
    max_retries      = optional(number, 3)
  }))
}

variable "job_order" {
  description = "List of job names in the order they should be executed in the workflow"
  type        = list(string)
  default     = []
}

variable "workflow_name" {
  description = "The name of the workflow"
  type        = string
}

variable "workflow_description" {
  description = "The description of the workflow"
  type        = string
}

variable "discord_workflow_id" {
  description = "The ID of the discord workflow"
  type        = string
}

variable "schedule_bkk" {
  description = "The schedule of the workflow in Bangkok timezone"
  type        = string
}
