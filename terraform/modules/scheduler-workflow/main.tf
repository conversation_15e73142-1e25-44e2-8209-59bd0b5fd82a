terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

resource "google_cloud_run_v2_job" "data_jobs" {
  for_each = var.jobs
  name     = each.key
  location = var.region

  labels = {
    cloud_run_type = "job"
    cloud_run_name = each.key
  }

  template {
    labels = {
      cloud_run_type = "job"
      cloud_run_name = each.key
    }

    template {
      timeout         = each.value.timeout
      max_retries     = each.value.max_retries
      service_account = "medeze-data-jobs-sa@${var.project_id}.iam.gserviceaccount.com"

      containers {
        name  = each.key
        image = var.container_image

        args = concat(
          ["${each.value.python_file_path}"],
          each.value.python_args
        )

        resources {
          limits = {
            cpu    = each.value.cpu
            memory = each.value.memory
          }
        }
      }

      vpc_access {
        connector = "projects/${var.project_id}/locations/${var.region}/connectors/${var.project_id}-connector"
        egress    = "ALL_TRAFFIC"
      }
    }
  }

  lifecycle {
    ignore_changes = [
      launch_stage,
    ]
  }
}

locals {
  # Use the provided job_order if specified, otherwise use the keys from the jobs map
  ordered_job_keys = length(var.job_order) > 0 ? var.job_order : keys(var.jobs)

  # Create the jobs list in the specified order
  jobs = [
    for key in local.ordered_job_keys : {
      name     = key
      location = var.region
    } if contains(keys(var.jobs), key) # Only include keys that exist in the jobs map
  ]
}

resource "google_workflows_workflow" "workflow" {
  name            = var.workflow_name
  description     = var.workflow_description
  project         = var.project_id
  region          = var.region
  service_account = "medeze-data-jobs-sa@${var.project_id}.iam.gserviceaccount.com"
  source_contents = templatefile("template/sequencial_jobs.yaml.tmpl", {
    tf_jobs                = local.jobs
    tf_discord_workflow_id = var.discord_workflow_id
    tf_workflow_name       = var.workflow_name
  })
}


resource "google_cloud_scheduler_job" "trigger_workflow" {
  name        = "trigger-${var.workflow_name}"
  description = "Triggers the ${var.workflow_name} workflow"
  project     = var.project_id
  region      = var.region

  schedule  = var.schedule_bkk
  time_zone = "Asia/Bangkok"

  http_target {
    http_method = "POST"
    uri         = "https://workflowexecutions.googleapis.com/v1/projects/${var.project_id}/locations/${var.region}/workflows/${google_workflows_workflow.workflow.name}/executions"

    oauth_token {
      service_account_email = "medeze-data-jobs-sa@${var.project_id}.iam.gserviceaccount.com"
    }

    headers = {
      "Content-Type" = "application/json"
    }
  }
}
