variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "region" {
  description = "The region where resources will be created"
  type        = string
  default     = "asia-southeast1"
}

variable "discord_workflow_id" {
  description = "The ID of the discord workflow"
  type        = string
}

variable "source_folder" {
  description = "The folder to trigger the workflow"
  type        = string
}

variable "workflow_name" {
  description = "The name of the workflow"
  type        = string
}

variable "workflow_description" {
  description = "The description of the workflow"
  type        = string
}

variable "container_image" {
  description = "The container image to use for the Cloud Run Job"
  type        = string
}

variable "job_name" {
  description = "The name of the Cloud Run Job"
  type        = string
}

variable "job_python_file_path" {
  description = "The python file path for the Cloud Run Job"
  type        = string
}


variable "job_cpu" {
  description = "The CPU allocation for the Cloud Run Job"
  type        = string
  default     = "1000m"
}

variable "job_memory" {
  description = "The memory allocation for the Cloud Run Job"
  type        = string
  default     = "512Mi"
}

variable "job_timeout" {
  description = "The timeout duration for the Cloud Run Job"
  type        = string
  default     = "14400s"
}

variable "job_max_retries" {
  description = "The maximum number of retries for the Cloud Run Job"
  type        = number
  default     = 3
}

variable "additional_jobs" {
  description = "Map of Cloud Run Jobs configurations"
  type = map(object({
    python_file_path = string
    python_args      = optional(list(string), [])
    cpu              = optional(string, "1000m")
    memory           = optional(string, "512Mi")
    timeout          = optional(string, "14400s")
    max_retries      = optional(number, 3)
  }))
}
