terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

// storage bucket is created in another repo
// service account iam is granted in another repo

// create pubsub topic
resource "google_pubsub_topic" "gcs_workflow" {
  name    = "topic-gcs-${var.workflow_name}"
  project = var.project_id
  labels = {
    created_by = "terraform"
  }
}

// create storage notification
resource "google_storage_notification" "gcs_to_pubsub" {
  bucket         = "${var.project_id}-datalake"
  topic          = google_pubsub_topic.gcs_workflow.id
  payload_format = "JSON_API_V1"

  event_types        = ["OBJECT_FINALIZE"]
  object_name_prefix = var.source_folder # Only trigger on source_folder
}

locals {
  additional_jobs = [
    for job_name, job_config in var.additional_jobs : {
      name             = job_name
      location         = var.region
      python_file_path = job_config.python_file_path
      python_args      = job_config.python_args
      cpu              = job_config.cpu
      memory           = job_config.memory
      timeout          = job_config.timeout
      max_retries      = job_config.max_retries
    }
  ]
}

// create workflow
resource "google_workflows_workflow" "gcs_workflow" {
  name            = var.workflow_name
  description     = var.workflow_description
  project         = var.project_id
  region          = var.region
  service_account = "medeze-data-jobs-sa@${var.project_id}.iam.gserviceaccount.com"
  source_contents = templatefile("template/gcs_uploads.yaml.tmpl", {
    tf_discord_workflow_id = var.discord_workflow_id
    tf_workflow_name       = var.workflow_name
    job_name               = var.job_name
    job_location           = var.region
    job_python_file_path   = var.job_python_file_path
    additional_jobs        = local.additional_jobs
  })
}

//create eventarc trigger
resource "google_eventarc_trigger" "gcs_to_workflow" {
  name     = "trigger-gcs-${var.workflow_name}"
  location = var.region

  matching_criteria {
    attribute = "type"
    value     = "google.cloud.pubsub.topic.v1.messagePublished"
  }

  transport {
    pubsub {
      topic = google_pubsub_topic.gcs_workflow.id
    }
  }

  destination {
    workflow = google_workflows_workflow.gcs_workflow.id
  }

  service_account = "medeze-data-jobs-sa@${var.project_id}.iam.gserviceaccount.com"
}

resource "google_cloud_run_v2_job" "data_jobs" {
  name     = var.job_name
  location = var.region

  labels = {
    cloud_run_type = "job"
    cloud_run_name = var.job_name
  }

  template {
    labels = {
      cloud_run_type = "job"
      cloud_run_name = var.job_name
    }

    template {
      timeout         = var.job_timeout
      max_retries     = var.job_max_retries
      service_account = "medeze-data-jobs-sa@${var.project_id}.iam.gserviceaccount.com"

      containers {
        name  = var.job_name
        image = var.container_image
        resources {
          limits = {
            cpu    = var.job_cpu
            memory = var.job_memory
          }
        }
      }

      vpc_access {
        connector = "projects/${var.project_id}/locations/${var.region}/connectors/${var.project_id}-connector"
        egress    = "ALL_TRAFFIC"
      }
    }
  }

  lifecycle {
    ignore_changes = [
      launch_stage,
    ]
  }
}

resource "google_cloud_run_v2_job" "additional_jobs" {
  for_each = var.additional_jobs
  name     = each.key
  location = var.region

  labels = {
    cloud_run_type = "job"
    cloud_run_name = each.key
  }

  template {
    labels = {
      cloud_run_type = "job"
      cloud_run_name = each.key
    }

    template {
      timeout         = each.value.timeout
      max_retries     = each.value.max_retries
      service_account = "medeze-data-jobs-sa@${var.project_id}.iam.gserviceaccount.com"

      containers {
        name  = each.key
        image = var.container_image
        args = concat(
          ["${each.value.python_file_path}"],
          each.value.python_args
        )
        resources {
          limits = {
            cpu    = each.value.cpu
            memory = each.value.memory
          }
        }
      }

      vpc_access {
        connector = "projects/${var.project_id}/locations/${var.region}/connectors/${var.project_id}-connector"
        egress    = "ALL_TRAFFIC"
      }
    }
  }

  lifecycle {
    ignore_changes = [
      launch_stage,
    ]
  }
}
