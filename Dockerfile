FROM ghcr.io/astral-sh/uv:python3.12-bookworm-slim

# Prevents Python from writing pyc files and keeps Python from buffering stdout/stderr
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy the project into the image
ADD . /app

# Set proper permissions and sync dependencies
WORKDIR /app
RUN chmod -R 755 /app && \
    uv sync --locked

# Install Playwright browsers
RUN uv run playwright install chromium --with-deps

# Use uv to run Python
ENTRYPOINT ["uv", "run"]
