import argparse
from lib.common.logger import logger

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Run ingestion job for a specific channel"
    )
    parser.add_argument("bucket_name", type=str, help="Bucket name (required)")

    parser.add_argument(
        "file_path", type=str, help="Path to the file uploaded (required)"
    )

    # Parse arguments
    args = parser.parse_args()

    logger.info("Starting the job...")
    logger.info(f"Bucket name: {args.bucket_name}")
    logger.info(f"File path: {args.file_path}")
