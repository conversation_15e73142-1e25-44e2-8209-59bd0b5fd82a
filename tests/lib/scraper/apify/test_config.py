import pytest
from datetime import datetime
import pendulum
from assertpy import assert_that
from lib.scraper.apify.config import (
    FacebookPostInputBuilder,
    FacebookCommentInputBuilder,
    InstagramPostInputBuilder,
    InstagramCommentInputBuilder,
    YoutubePostInputBuilder,
    YoutubeCommentInputBuilder,
    TwitterPostInputBuilder,
    TwitterCommentInputBuilder,
    TikTokPostInputBuilder,
    TikTokCommentInputBuilder,
    get_actor_config,
)


@pytest.fixture
def sample_search_single_keyword():
    return ["test1", "test2", "test3"]


@pytest.fixture
def sample_search_multiple_keywords():
    return ["test11+test12", "test21+test22", "test31+test32", "test41+test42 test43"]


@pytest.fixture
def sample_search_mix_keywords():
    return ["test11+test12", "test21", "test31+test32"]


@pytest.fixture
def mock_pendulum_now(monkeypatch):
    """Mock pendulum.now() to return a fixed date."""

    class MockPendulum:
        @staticmethod
        def now():
            return pendulum.datetime(2024, 1, 1)

        @staticmethod
        def to_date_string():
            return "2024-01-01"

        @staticmethod
        def subtract(days):
            return pendulum.datetime(2023, 12, 29)

    monkeypatch.setattr(pendulum, "now", MockPendulum.now)
    return MockPendulum


def test_facebook_post_input_builder_single_keyword(
    sample_search_single_keyword, mock_pendulum_now
):
    max_posts = 5
    days_lookback = 3
    builder = FacebookPostInputBuilder(max_posts=max_posts, days_lookback=days_lookback)
    search_terms = builder.create_search_term(sample_search_single_keyword)
    expect_search_terms = ["test1", "test2", "test3"]
    assert_that(search_terms).is_type_of(list)
    assert_that(search_terms).is_length(len(sample_search_single_keyword))
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "query": "test1",
        "start_date": "2023-12-29",
        "end_date": "2024-01-01",
        "max_posts": 5,
        "search_type": "posts",
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_facebook_post_input_builder_multiple_keywords(
    sample_search_multiple_keywords, mock_pendulum_now
):
    max_posts = 5
    days_lookback = 3
    builder = FacebookPostInputBuilder(max_posts=max_posts, days_lookback=days_lookback)

    search_terms = builder.create_search_term(sample_search_multiple_keywords)
    expect_search_terms = [
        "test11 test12",
        "test21 test22",
        "test31 test32",
        "test41 test42 test43",
    ]

    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "query": "test11 test12",
        "start_date": "2023-12-29",
        "end_date": "2024-01-01",
        "max_posts": 5,
        "search_type": "posts",
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_facebook_post_input_builder_mix_keywords(
    sample_search_mix_keywords, mock_pendulum_now
):
    max_posts = 5
    days_lookback = 3
    builder = FacebookPostInputBuilder(max_posts=max_posts, days_lookback=days_lookback)

    search_terms = builder.create_search_term(sample_search_mix_keywords)
    expect_search_terms = ["test11 test12", "test21", "test31 test32"]

    assert_that(search_terms).is_equal_to(expect_search_terms)


def test_facebook_comment_input_builder(sample_search_single_keyword):
    builder = FacebookCommentInputBuilder()
    search_terms = builder.create_search_term(sample_search_single_keyword)
    expect_search_terms = ["test1,test2,test3"]
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "startUrls": [
            {"url": "test1", "method": "GET"},
            {"url": "test2", "method": "GET"},
            {"url": "test3", "method": "GET"},
        ],
        "includeNestedComments": True,
        "resultsLimit": 15000,
        "viewOption": "RANKED_UNFILTERED",
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_instagram_post_input_builder_single_keyword(
    sample_search_single_keyword, mock_pendulum_now
):
    days_lookback = 3
    max_items = 10
    builder = InstagramPostInputBuilder(
        max_items=max_items, days_lookback=days_lookback
    )
    search_terms = builder.create_search_term(sample_search_single_keyword)
    expect_search_terms = [
        "https://www.instagram.com/explore/tags/test1,https://www.instagram.com/explore/tags/test2,https://www.instagram.com/explore/tags/test3",
    ]
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "startUrls": [
            "https://www.instagram.com/explore/tags/test1",
            "https://www.instagram.com/explore/tags/test2",
            "https://www.instagram.com/explore/tags/test3",
        ],
        "until": "2023-12-29",  # 3 days before 2024-01-01
        "maxItems": 10,
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_instagram_post_input_builder_multiple_keywords(
    sample_search_multiple_keywords, mock_pendulum_now
):
    days_lookback = 3
    max_items = 10
    builder = InstagramPostInputBuilder(
        max_items=max_items, days_lookback=days_lookback
    )
    search_terms = builder.create_search_term(sample_search_multiple_keywords)
    expect_search_terms = [
        "https://www.instagram.com/explore/tags/test11test12,https://www.instagram.com/explore/tags/test21test22,https://www.instagram.com/explore/tags/test31test32,https://www.instagram.com/explore/tags/test41test42test43",
    ]
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "startUrls": [
            "https://www.instagram.com/explore/tags/test11test12",
            "https://www.instagram.com/explore/tags/test21test22",
            "https://www.instagram.com/explore/tags/test31test32",
            "https://www.instagram.com/explore/tags/test41test42test43",
        ],
        "until": "2023-12-29",  # 3 days before 2024-01-01
        "maxItems": 10,
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_instagram_post_input_builder_mix_keywords(
    sample_search_mix_keywords, mock_pendulum_now
):
    builder = InstagramPostInputBuilder()
    search_terms = builder.create_search_term(sample_search_mix_keywords)
    expect_search_terms = [
        "https://www.instagram.com/explore/tags/test11test12,https://www.instagram.com/explore/tags/test21,https://www.instagram.com/explore/tags/test31test32",
    ]
    assert_that(search_terms).is_equal_to(expect_search_terms)


def test_instagram_comment_input_builder(sample_search_single_keyword):
    builder = InstagramCommentInputBuilder()
    search_terms = builder.create_search_term(sample_search_single_keyword)
    expect_search_terms = ["test1,test2,test3"]
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "startUrls": ["test1", "test2", "test3"],
        "maxComments": 1000,
        "includeReplies": True,
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_youtube_post_input_builder_single_keyword(sample_search_single_keyword):
    builder = YoutubePostInputBuilder()
    search_terms = builder.create_search_term(sample_search_single_keyword)
    expect_search_terms = ['"test1","test2","test3"']
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "searchQueries": ['"test1"', '"test2"', '"test3"'],
        "dateFilter": "week",
        "sortingOrder": "date",
        "maxResultStreams": 50,
        "maxResults": 50,
        "maxResultsShorts": 50,
        "downloadSubtitles": False,
        "hasCC": False,
        "hasLocation": False,
        "hasSubtitles": False,
        "is360": False,
        "is3D": False,
        "is4K": False,
        "isBought": False,
        "isHD": False,
        "isHDR": False,
        "isLive": False,
        "isVR180": False,
        "preferAutoGeneratedSubtitles": False,
        "saveSubsToKVS": False,
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_youtube_post_input_builder_multiple_keywords(sample_search_multiple_keywords):
    builder = YoutubePostInputBuilder()
    search_terms = builder.create_search_term(sample_search_multiple_keywords)
    expect_search_terms = [
        '"test11"+"test12","test21"+"test22","test31"+"test32","test41"+"test42 test43"'
    ]
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "searchQueries": [
            '"test11"+"test12"',
            '"test21"+"test22"',
            '"test31"+"test32"',
            '"test41"+"test42 test43"',
        ],
        "dateFilter": "week",
        "sortingOrder": "date",
        "maxResultStreams": 50,
        "maxResults": 50,
        "maxResultsShorts": 50,
        "downloadSubtitles": False,
        "hasCC": False,
        "hasLocation": False,
        "hasSubtitles": False,
        "is360": False,
        "is3D": False,
        "is4K": False,
        "isBought": False,
        "isHD": False,
        "isHDR": False,
        "isLive": False,
        "isVR180": False,
        "preferAutoGeneratedSubtitles": False,
        "saveSubsToKVS": False,
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_youtube_post_input_builder_mix_keywords(sample_search_mix_keywords):
    builder = YoutubePostInputBuilder()
    search_terms = builder.create_search_term(sample_search_mix_keywords)
    expect_search_terms = ['"test11"+"test12","test21","test31"+"test32"']
    assert_that(search_terms).is_equal_to(expect_search_terms)


def test_youtube_comment_input_builder(sample_search_single_keyword):
    builder = YoutubeCommentInputBuilder()
    search_terms = builder.create_search_term(sample_search_single_keyword)
    expect_search_terms = ["test1,test2,test3"]
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "startUrls": [
            {"url": "test1", "method": "GET"},
            {"url": "test2", "method": "GET"},
            {"url": "test3", "method": "GET"},
        ],
        "commentsSortBy": "1",
        "maxComments": 1000,
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_twitter_post_input_builder_single_keyword(
    sample_search_single_keyword, mock_pendulum_now
):
    days_lookback = 3
    max_items = 80
    builder = TwitterPostInputBuilder(max_items=max_items, days_lookback=days_lookback)
    search_terms = builder.create_search_term(sample_search_single_keyword)
    expect_search_terms = ['"test1" OR "test2" OR "test3"']
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "searchTerms": ['"test1" OR "test2" OR "test3"'],
        "start": "2023-12-29",  # 3 days before 2024-01-01
        "end": "2024-01-01",
        "maxItems": 80,
        "sort": "Latest",
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_twitter_post_input_builder_multiple_keywords(
    sample_search_multiple_keywords, mock_pendulum_now
):
    days_lookback = 3
    max_items = 80
    builder = TwitterPostInputBuilder(max_items=max_items, days_lookback=days_lookback)
    search_terms = builder.create_search_term(sample_search_multiple_keywords)
    expect_search_terms = [
        '"test11 test12" OR "test21 test22" OR "test31 test32" OR "test41 test42 test43"'
    ]
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "searchTerms": [
            '"test11 test12" OR "test21 test22" OR "test31 test32" OR "test41 test42 test43"'
        ],
        "start": "2023-12-29",  # 3 days before 2024-01-01
        "end": "2024-01-01",
        "maxItems": 80,
        "sort": "Latest",
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_twitter_post_input_builder_mix_keywords(
    sample_search_mix_keywords, mock_pendulum_now
):
    days_lookback = 3
    max_items = 80
    builder = TwitterPostInputBuilder(max_items=max_items, days_lookback=days_lookback)
    search_terms = builder.create_search_term(sample_search_mix_keywords)
    expect_search_terms = ['"test11 test12" OR "test21" OR "test31 test32"']
    assert_that(search_terms).is_equal_to(expect_search_terms)


def test_twitter_comment_input_builder(sample_search_single_keyword):

    builder = TwitterCommentInputBuilder()
    search_terms = builder.create_search_term(sample_search_single_keyword)
    expect_search_terms = [
        "conversation_id:test1,conversation_id:test2,conversation_id:test3"
    ]
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "searchTerms": [
            "conversation_id:test1",
            "conversation_id:test2",
            "conversation_id:test3",
        ],
        "start": None,
        "end": None,
        "maxItems": 800,
        "sort": "Latest",
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_tiktok_post_input_builder_single_keyword(sample_search_single_keyword):
    builder = TikTokPostInputBuilder()
    search_terms = builder.create_search_term(sample_search_single_keyword)
    expect_search_terms = ["test1,test2,test3"]
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "keywords": ["test1", "test2", "test3"],
        "dateRange": "THIS_WEEK",
        "includeSearchKeywords": True,
        "maxItems": 1000,
        "sortType": "DATE_POSTED",
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_tiktok_post_input_builder_multiple_keywords(sample_search_multiple_keywords):
    builder = TikTokPostInputBuilder()
    search_terms = builder.create_search_term(sample_search_multiple_keywords)
    expect_search_terms = [
        "test11 test12,test21 test22,test31 test32,test41 test42 test43"
    ]
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "keywords": [
            "test11 test12",
            "test21 test22",
            "test31 test32",
            "test41 test42 test43",
        ],
        "dateRange": "THIS_WEEK",
        "includeSearchKeywords": True,
        "maxItems": 1000,
        "sortType": "DATE_POSTED",
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_tiktok_post_input_builder_mix_keywords(sample_search_mix_keywords):
    builder = TikTokPostInputBuilder()
    search_terms = builder.create_search_term(sample_search_mix_keywords)
    expect_search_terms = ["test11 test12,test21,test31 test32"]
    assert_that(search_terms).is_equal_to(expect_search_terms)


def test_tiktok_comment_input_builder(sample_search_single_keyword):
    builder = TikTokCommentInputBuilder()
    search_terms = builder.create_search_term(sample_search_single_keyword)
    expect_search_terms = ["test1,test2,test3"]
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "startUrls": ["test1", "test2", "test3"],
        "includeReplies": True,
        "maxItems": 1000,
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_get_actor_config():
    # Test valid actor
    actor_id, memory, input_builder = get_actor_config("facebook-post")
    assert_that(actor_id).is_equal_to("danek/facebook-search-ppr")
    assert_that(memory).is_equal_to(1024)
    assert_that(input_builder).is_instance_of(FacebookPostInputBuilder)

    # Test invalid actor
    assert_that(lambda: get_actor_config("invalid-actor")).raises(ValueError)


def test_empty_search_items():
    empty_items = []
    builders = [
        FacebookPostInputBuilder(),
        FacebookCommentInputBuilder(),
        InstagramPostInputBuilder(),
        InstagramCommentInputBuilder(),
        YoutubePostInputBuilder(),
        YoutubeCommentInputBuilder(),
        TwitterPostInputBuilder(),
        TikTokPostInputBuilder(),
        TikTokCommentInputBuilder(),
    ]

    for builder in builders:
        search_terms = builder.create_search_term(empty_items)
        assert_that(search_terms).is_type_of(list)
        assert_that(search_terms).is_empty()
