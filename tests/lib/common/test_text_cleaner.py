import unittest
from lib.common.text_cleaner import TextCleaner


class TestTextCleaner(unittest.TestCase):
    """Test cases for TextCleaner class."""

    def test_remove_invisible_unicode(self):
        """Test removal of invisible unicode characters."""
        # Test with invisible unicode characters
        text_with_invisible = "Hello\u200bWorld\u200c\u200d\u200e\u200f\u202a\u202b\u202c\u202d\u202e\u2060\uFEFFTest"
        expected = "HelloWorldTest"
        result = TextCleaner.remove_invisible_unicode(text_with_invisible)
        self.assertEqual(result, expected)

        # Test with Thai characters (should be preserved)
        thai_text = "สวัสดี\u200bครับ\u200cผม\u200dชื่อ\u200eจอห์น"
        expected_thai = "สวัสดีครับผมชื่อจอห์น"
        result_thai = TextCleaner.remove_invisible_unicode(thai_text)
        self.assertEqual(result_thai, expected_thai)

        # Test with normal text (should remain unchanged)
        normal_text = "Hello World"
        result_normal = TextCleaner.remove_invisible_unicode(normal_text)
        self.assertEqual(result_normal, normal_text)

        # Test with empty string
        result_empty = TextCleaner.remove_invisible_unicode("")
        self.assertEqual(result_empty, "")

    def test_remove_hashtags_with_none(self):
        """Test remove_hashtags with None parameter (should remove all hashtags)."""
        text = "Hello #world this is a #test #message"
        expected = "Hello  this is a  "
        result = TextCleaner.remove_hashtags(text, None)
        self.assertEqual(result, expected)

        # Test with default parameter (None)
        result_default = TextCleaner.remove_hashtags(text)
        self.assertEqual(result_default, expected)

    def test_remove_hashtags_with_empty_list(self):
        """Test remove_hashtags with empty list (should remove all hashtags)."""
        text = "Hello #world this is a #test #message"
        expected = "Hello  this is a  "
        result = TextCleaner.remove_hashtags(text, [])
        self.assertEqual(result, expected)

    def test_remove_hashtags_with_keep_list(self):
        """Test remove_hashtags with specific hashtags to keep."""
        text = "Hello #world this is a #test #message #WORLD"
        keep_hashtags = ["world", "message"]
        expected = "Hello #world this is a  #message #WORLD"
        result = TextCleaner.remove_hashtags(text, keep_hashtags)
        self.assertEqual(result, expected)

    def test_remove_hashtags_case_insensitive(self):
        """Test that hashtag matching is case insensitive."""
        text = "Hello #World #TEST #world #test"
        keep_hashtags = ["world"]
        expected = "Hello #World  #world "
        result = TextCleaner.remove_hashtags(text, keep_hashtags)
        self.assertEqual(result, expected)

    def test_remove_hashtags_with_special_characters(self):
        """Test hashtags with special characters and numbers."""
        text = "Check #covid19 and #covid-19 and #test_case #123numbers"
        keep_hashtags = ["covid19", "123numbers"]
        expected = "Check #covid19 and  and  #123numbers"
        result = TextCleaner.remove_hashtags(text, keep_hashtags)
        self.assertEqual(result, expected)

    def test_remove_hashtags_no_hashtags(self):
        """Test text without hashtags."""
        text = "This is a normal text without hashtags"
        result = TextCleaner.remove_hashtags(text, ["test"])
        self.assertEqual(result, text)

    def test_remove_hashtags_empty_string(self):
        """Test with empty string."""
        result = TextCleaner.remove_hashtags("", ["test"])
        self.assertEqual(result, "")

    def test_remove_links(self):
        """Test removal of HTTP/HTTPS links."""
        # Test with HTTP links
        text_http = "Check this out http://example.com and this https://test.com"
        expected_http = "Check this out  and this "
        result_http = TextCleaner.remove_links(text_http)
        self.assertEqual(result_http, expected_http)

        # Test with complex URLs
        text_complex = "Visit https://example.com/path?param=value&other=test for more info"
        expected_complex = "Visit  for more info"
        result_complex = TextCleaner.remove_links(text_complex)
        self.assertEqual(result_complex, expected_complex)

        # Test with no links
        text_no_links = "This is a normal text"
        result_no_links = TextCleaner.remove_links(text_no_links)
        self.assertEqual(result_no_links, text_no_links)

        # Test with empty string
        result_empty = TextCleaner.remove_links("")
        self.assertEqual(result_empty, "")

    def test_remove_extra_spaces(self):
        """Test removal of extra whitespace."""
        # Test with multiple spaces
        text_spaces = "Hello    world   this  is   a    test"
        expected_spaces = "Hello world this is a test"
        result_spaces = TextCleaner.remove_extra_spaces(text_spaces)
        self.assertEqual(result_spaces, expected_spaces)

        # Test with tabs and newlines
        text_mixed = "Hello\t\tworld\n\nthis\r\nis\n\n\na\t\t\ttest"
        expected_mixed = "Hello world this is a test"
        result_mixed = TextCleaner.remove_extra_spaces(text_mixed)
        self.assertEqual(result_mixed, expected_mixed)

        # Test with leading and trailing spaces
        text_trim = "   Hello world   "
        expected_trim = "Hello world"
        result_trim = TextCleaner.remove_extra_spaces(text_trim)
        self.assertEqual(result_trim, expected_trim)

        # Test with only whitespace
        text_only_spaces = "   \t\n\r   "
        expected_only_spaces = ""
        result_only_spaces = TextCleaner.remove_extra_spaces(text_only_spaces)
        self.assertEqual(result_only_spaces, expected_only_spaces)

        # Test with normal text
        text_normal = "Hello world"
        result_normal = TextCleaner.remove_extra_spaces(text_normal)
        self.assertEqual(result_normal, text_normal)

        # Test with empty string
        result_empty = TextCleaner.remove_extra_spaces("")
        self.assertEqual(result_empty, "")

    def test_combined_cleaning(self):
        """Test combining multiple cleaning methods."""
        # Test a realistic scenario with all types of issues
        messy_text = (
            "Hello\u200b #world   check this http://example.com   "
            "#test\u200c message\t\twith\n\nextra   spaces"
        )
        
        # Step by step cleaning
        step1 = TextCleaner.remove_invisible_unicode(messy_text)
        step2 = TextCleaner.remove_hashtags(step1, ["world"])
        step3 = TextCleaner.remove_links(step2)
        result = TextCleaner.remove_extra_spaces(step3)
        
        expected = "Hello #world check this message with extra spaces"
        self.assertEqual(result, expected)


if __name__ == "__main__":
    unittest.main()
