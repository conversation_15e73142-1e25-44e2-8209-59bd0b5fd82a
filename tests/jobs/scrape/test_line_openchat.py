import pytest
from jobs.scrape.line_openchat import LineTransform<PERSON><PERSON>, LineOpenChatData
import datetime


class TestLineTransformJob:
    @pytest.fixture
    def line_job(self):
        """Create a LineTransformJob instance for testing."""
        return LineTransformJob(
            bucket_name="test_bucket", file_path="test/group_id/file.txt"
        )

    @pytest.fixture
    def sample_line_data(self):
        """Create sample LineOpenChatData instances for testing."""
        return [
            LineOpenChatData(
                group_id="test_group",
                conversation_id="conv_1",
                timestamp=datetime.datetime.now(),
                author_name="User1",
                content="Hello world",
                content_order=1,
            ),
            LineOpenChatData(
                group_id="test_group",
                conversation_id="conv_2",
                timestamp=datetime.datetime.now(),
                author_name="User2",
                content="Photos",
                content_order=2,
            ),
            LineOpenChatData(
                group_id="test_group",
                conversation_id="conv_3",
                timestamp=datetime.datetime.now(),
                author_name="User3",
                content="Good morning",
                content_order=3,
            ),
        ]

    @pytest.mark.parametrize(
        "input_text,expected",
        [
            # Single date section
            (
                "2025.06.19 Thursday\nMessage 1\nMessage 2",
                [{"date_part": "2025-06-19", "full_text": "Message 1\nMessage 2"}],
            ),
            # Multiple date sections
            (
                "2025.06.19 Thursday\nMessage 1\n2025.06.20 Friday\nMessage 2\nMessage 3",
                [
                    {"date_part": "2025-06-19", "full_text": "Message 1"},
                    {"date_part": "2025-06-20", "full_text": "Message 2\nMessage 3"},
                ],
            ),
            # Wed, DD/MM/YYYY format
            (
                "Wed, 23/04/2025\nMessage from Wednesday\nAnother message",
                [
                    {
                        "date_part": "2025-04-23",
                        "full_text": "Message from Wednesday\nAnother message",
                    }
                ],
            ),
            # Multiple Wed, DD/MM/YYYY format
            (
                "Wed, 23/04/2025\nMessage 1\nThu, 24/04/2025\nMessage 2",
                [
                    {"date_part": "2025-04-23", "full_text": "Message 1"},
                    {"date_part": "2025-04-24", "full_text": "Message 2"},
                ],
            ),
            # Mixed date formats
            (
                "2025.06.19 Thursday\nDot format message\nWed, 23/04/2025\nSlash format message",
                [
                    {"date_part": "2025-06-19", "full_text": "Dot format message"},
                    {"date_part": "2025-04-23", "full_text": "Slash format message"},
                ],
            ),
            # Empty input
            ("", []),
            # No date headers
            ("Just some text without dates", []),
            # Date with extra whitespace
            (
                "2025.01.01 Monday\n  Message with spaces  \n2025.01.02 Tuesday\n  Another message  ",
                [
                    {"date_part": "2025-01-01", "full_text": "Message with spaces"},
                    {"date_part": "2025-01-02", "full_text": "Another message"},
                ],
            ),
        ],
    )
    def test_split_text_by_date(self, line_job, input_text, expected):
        """Test the split_text_by_date method with various inputs."""
        result = line_job.split_text_by_date(input_text)
        assert result == expected

    def test_clean_response_with_nested_lists(self, line_job, sample_line_data):
        """Test clean_response with nested lists and None values."""
        input_data = [
            [sample_line_data[0], sample_line_data[1]],
            None,
            [sample_line_data[2]],
            [],
        ]

        result = line_job.clean_response(input_data)

        # Should flatten and remove None, empty lists, and "Photos" content
        assert len(result) == 2
        assert result[0].content == "Hello world"
        assert result[1].content == "Good morning"

    def test_clean_response_filters_photos(self, line_job, sample_line_data):
        """Test that clean_response filters out items with 'Photos' content."""
        input_data = [[sample_line_data[0], sample_line_data[1], sample_line_data[2]]]

        result = line_job.clean_response(input_data)

        # Should exclude the item with "Photos" content
        assert len(result) == 2
        assert all(item.content != "Photos" for item in result)

    def test_clean_response_empty_input(self, line_job):
        """Test clean_response with empty input."""
        result = line_job.clean_response([])
        assert result == []

    def test_clean_response_all_none(self, line_job):
        """Test clean_response with all None values."""
        input_data = [None, None, None]
        result = line_job.clean_response(input_data)
        assert result == []

    @pytest.mark.parametrize(
        "input_text,expected",
        [
            # Case variations of "medeze"
            ("medeze", True),
            ("MEDEZE", True),
            ("Medeze", True),
            ("MeDeZe", True),
            # Thai keyword
            ("เมดีซ", True),
            # Keywords in sentences
            ("This is about medeze company", True),
            ("MEDEZE stock is rising", True),
            # Real LINE chat format with medeze
            (
                "12:58	นล	พวกนี้เน้นของใหม่ออกมา ดึงลูกค้าเป็นช่วงๆ 13:02	TooN	https://youtu.be/9eIHEX_h2Uc?si=NttLDd2MldiOiwr9 13:02	TooN	คนโสดไม่มีลูก อีก 20 ปีคงมีหุ่นยนต์มาดูแล ครับ",
                False,
            ),
            (
                "12:58	นล	พวกนี้เน้นของใหม่ออกมา ดึงลูกค้าเป็นช่วงๆ 13:02	TooN	https://youtu.be/9eIHEX_h2Uc?si=NttLDd2MldiOiwr9 13:02	TooN	คนโสดไม่มีลูก อีก 20 MEDEZE ปีคงมีหุ่นยนต์มาดูแล ครับ",
                True,
            ),
            (
                '15:27   Benz_Trader     "Strategy Update - SET Index แกว่งชะลอ หลังจากขึ้นทดสอบกรอบ 1,150-1,160 จุด เพื่อรอดูพัฒนาการเจรจาการค้าระหว่างไทย-สหรัฐฯ คาดหุ้นที่ผลประกอบการ 2Q25 จะออกมาสวย มีโอกาส Outperform ตลาดในช่วงนี้ เช่น ADVANC, BAM, TRUE, CPAXT, OSP, MEDEZE เป็นต้น',
                True,
            ),
            # Mixed case in sentences
            ("The MeDeze product is good", True),
            # No keywords
            ("This is just regular text", False),
            ("medicine is good", False),
            ("medical treatment", False),
            # Empty string
            ("", False),
            # Keywords with extra spaces
            ("  medeze  ", True),
            ("  MEDEZE  ", True),
        ],
    )
    def test_has_medeze(self, line_job, input_text, expected):
        """Test the has_medeze method with various inputs."""
        result = line_job.has_medeze(input_text)
        assert result == expected
