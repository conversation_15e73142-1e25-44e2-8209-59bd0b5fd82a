import time
from typing import List
import re
from playwright.sync_api import sync_playwright, Locator
from lib.common.logger import logger
import uuid


class ThaiViScraper:

    BASE_URL = "https://board.thaivi.org"

    def __init__(
        self,
        username: str,
        password: str,
        keyword: str,
        pages_lookback: int | None = None,
    ):
        """
        Initialize the ThaiVI scraper.

        Args:
            username: ThaiVI login username
            password: ThaiVI login password
        """
        self.username = username
        self.password = password
        self.keyword = keyword
        self.pages_lookback = pages_lookback
        self.browser = None
        self.context = None
        self.page = None

    def setup_browser(self, headless: bool = True) -> None:
        """
        Set up the browser instance.

        Args:
            headless: Whether to run browser in headless mode
        """
        playwright = sync_playwright().start()
        self.browser = playwright.chromium.launch(headless=headless)
        self.context = self.browser.new_context()
        self.page = self.context.new_page()

    def login(self) -> bool:
        """
        Log in to ThaiVI forum.

        Returns:
            bool: True if login successful, False otherwise
        """
        try:
            # Navigate to the base URL
            self.page.goto(self.BASE_URL, wait_until="networkidle")

            # Find and click login button
            self.page.wait_for_selector("a.btn-login", state="visible", timeout=10000)
            login_button = self.page.locator("a.btn-login")
            login_button.click()
            self.page.wait_for_load_state("networkidle")

            # Fill in credentials
            self.page.wait_for_selector(
                "input.button.btn-login", state="visible", timeout=10000
            )
            self.page.fill('input[name="username"]', self.username)
            self.page.fill('input[name="password"]', self.password)

            # Submit login form
            submit_login_button = self.page.locator("input.button.btn-login")
            submit_login_button.click()

            # Wait for navigation
            self.page.wait_for_load_state("networkidle")
            self.page.wait_for_timeout(2000)  # Add 2 second wait after login

            return True
        except Exception as e:
            logger.error(f"Login failed: {e}")
            return False

    def search(self, keyword: str) -> bool:
        """
        Search for a keyword on ThaiVI forum.

        Args:
            keyword: The keyword to search for

        Returns:
            bool: True if search successful, False otherwise
        """
        try:
            # Wait for search input field
            search_input_elem = '//input[@type="search" and @id="keywords"]'
            self.page.wait_for_selector(
                search_input_elem,
                state="visible",
                timeout=5000,
            )
            self.page.fill(search_input_elem, keyword)

            # Click search button
            search_button_elem = '//input[@type="submit" and @id="searchsubmit"]'
            self.page.wait_for_selector(
                search_button_elem,
                state="visible",
                timeout=5000,
            )
            self.page.locator(search_button_elem).click()

            return True
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return False

    def get_total_pages(self) -> int:
        """
        Get the total number of search result pages.

        Returns:
            int: Total number of pages
        """
        try:
            # Wait for the page number input field
            self.page.wait_for_selector("#pagenumber-up", state="visible", timeout=5000)
            # Get the value of the input field
            page_number = self.page.locator("#pagenumber-up").input_value()
            return int(page_number)
        except Exception as e:
            logger.error(f"Failed to get total pages: {e}")
            return 0

    def navigate_to_page(self, page_num: int) -> bool:
        """
        Navigate to a specific page number.

        Args:
            page_num: The page number to navigate to

        Returns:
            bool: True if navigation successful, False otherwise
        """
        try:
            logger.info(f"Navigating to page {page_num}...")
            # Wait for the page number input field
            self.page.wait_for_selector("#pagenumber-up", state="visible", timeout=5000)
            # Clear existing value and input the new page number
            self.page.fill("#pagenumber-up", str(page_num))
            # Press "Enter" to trigger the page change
            self.page.locator("#pagenumber-up").press("Enter")
            self.page.wait_for_load_state("domcontentloaded")
            time.sleep(3)  # Allow page to load
            return True
        except Exception as e:
            logger.error(f"Failed to navigate to page {page_num}: {e}")
            return False

    def extract_post_data(self, post: Locator) -> dict:
        try:
            post_id = post.get_attribute("id")

            # Wait for content to be visible with reduced timeout
            try:
                post.locator(".content").wait_for(state="visible", timeout=5000)
            except Exception:
                logger.warning(f"Content not visible for post {post_id}")

            # Extract post content with retry mechanism
            content = ""
            retry_count = 0
            max_retries = 2  # Reduced retries

            while retry_count < max_retries:
                try:
                    content = post.locator(".content").inner_text(
                        timeout=5000
                    )  # Reduced timeout
                    break
                except Exception as e:
                    retry_count += 1
                    logger.warning(
                        f"Retry {retry_count}/{max_retries} getting content: {e}"
                    )
                    if retry_count < max_retries:
                        self.page.wait_for_timeout(1000)  # Reduced wait

            # Extract image URL if present
            image_url = None
            image_elem = post.locator(".attach-image img")
            if image_elem.count() > 0:
                # Handle multiple images
                for i in range(image_elem.count()):
                    image_url = image_elem.nth(i).get_attribute("src")
                    if image_url:
                        # Convert relative URL to absolute
                        image_url = f"{self.BASE_URL}/{image_url.lstrip('./')}"
                        content += f"\n[Image: {image_url}]"

            # Extract video URL if present
            video_url = None
            video_elem = post.locator(".bbvideo")
            if video_elem.count() > 0:
                # Handle multiple videos
                for i in range(video_elem.count()):
                    video_url = video_elem.nth(i).get_attribute("data-url")
                    if video_url:
                        content += f"\n[Video: {video_url}]"

            # Extract post author with retry
            author = ""
            retry_count = 0
            while retry_count < max_retries:
                try:
                    author = (
                        post.locator(".postprofile dt")
                        .inner_text(timeout=3000)  # Reduced timeout
                        .strip()
                    )
                    break
                except Exception:
                    retry_count += 1
                    logger.warning(f"Retry {retry_count}/{max_retries} getting author")
                    if retry_count < max_retries:
                        self.page.wait_for_timeout(500)

            # Extract post number
            post_number_elem = post.locator(".post-comment-no")
            post_number = ""
            if post_number_elem.count() > 1:
                text = post_number_elem.nth(1).inner_text()
                match = re.search(r"\d+", text)
                if match:
                    post_number = match.group(0)

            # Extract post date
            post_date = ""
            try:
                post_date = post.locator(".post-date time").get_attribute(
                    "datetime", timeout=2000
                )
            except Exception:
                logger.warning(f"Could not extract date for post {post_id}")

            # Extract likes count
            likes_count = 0
            likes_count_elem = post.locator(f".like-amount-{post_id.replace('p', '')}")
            if likes_count_elem.count() > 0:
                likes_text = likes_count_elem.inner_text()
                if likes_text and likes_text.isdigit():
                    likes_count = int(likes_text)

            # Extract comments count
            comments_text = (
                post.locator(f"#sub_space_comment{post_id.replace('p', '')} + span")
                .inner_text()
                .strip()
            )
            comments_count = 0
            if comments_text:
                # Extract number from text like "3 คอมเมนต์"
                comments_match = re.search(r"(\d+)", comments_text)
                if comments_match:
                    comments_count = int(comments_match.group(1))

            post_data = {
                "id": post_id,
                "author_name": author,
                "post_number": post_number,
                "timestamp": post_date,
                "content": content,
                "like_count": likes_count,
                "comment_count": comments_count,
            }

            return post_data
        except Exception as e:
            logger.error(f"Error in extract_post_data: {e}")
            # Return a minimal post data object to avoid breaking the flow
            return {
                "id": post.get_attribute("id") or f"unknown-{uuid.uuid4()}",
                "author_name": "",
                "post_number": "",
                "timestamp": "",
                "content": f"[Error extracting content: {str(e)}]",
                "like_count": 0,
                "comment_count": 0,
            }

    def extract_posts_on_page(self) -> List[dict]:
        try:
            # Wait for page to be fully loaded with timeout
            try:
                self.page.wait_for_load_state("networkidle", timeout=10000)
            except Exception:
                logger.warning("Page did not reach networkidle state")

            self.page.wait_for_timeout(1000)  # Reduced wait time

            # Check if posts exist
            posts_selector = ".post.has-profile.bg1, .post.has-profile.bg2"

            try:
                self.page.wait_for_selector(
                    posts_selector, state="visible", timeout=5000
                )
            except Exception:
                logger.warning("Posts selector not found, trying alternative approach")
                return []

            posts = self.page.locator(posts_selector)
            posts_count = posts.count()

            if posts_count == 0:
                return []

            logger.info(f"Found {posts_count} posts on the page")
            extracted_data = []

            for i in range(posts_count):
                try:
                    post = posts.nth(i)
                    post_data = self.extract_post_data(post)
                    extracted_data.append(post_data)
                    logger.info(f"Extracted post {i+1}/{posts_count}")
                except Exception as e:
                    logger.error(f"Error extracting post {i+1}: {e}")
                    continue

            return extracted_data
        except Exception as e:
            logger.error(f"Failed to extract posts: {e}")
            return []

    def extract_comments_data(self, comment: Locator) -> dict:
        comment_id = comment.get_attribute("id")

        # Extract parent post id
        comment_parent_id = comment.get_attribute("data-id")

        # Extract comment content
        content = comment.locator(".content").inner_text()

        # Extract comment date
        comment_date = comment.locator(".subcomment-date").inner_text()

        # Extract author
        author = comment.locator(".username-coloured font").inner_text()

        # Extract post number
        comment_number = re.search(
            r"(\d+(?:-\d+)?)", comment.locator(".subcomment-no").inner_text()
        ).group(1)

        comment_data = {
            "id": comment_id,
            "post_id": comment_parent_id,
            "comment_number": comment_number,
            "author_name": author,
            "timestamp": comment_date,
            "content": content,
        }

        return comment_data

    def extract_comments_on_page(self) -> List[dict]:
        """
        Extract comment data from the current page.

        Returns:
            List[str]: List of post texts
        """

        try:
            # Find all comments
            comments = self.page.locator(".box-sub-comment-old:has(.content)")
            comments_count = comments.count()

            logger.info(f"Found {comments_count} comments on the page")

            extracted_data = []

            for i in range(comments_count):
                comment = comments.nth(i)
                comment_data = self.extract_comments_data(comment)
                extracted_data.append(comment_data)
            return extracted_data
        except Exception as e:
            logger.error(f"Failed to extract posts: {e}")
            return []

    def click_all_comment_buttons(self) -> bool:
        """
        Click all comment buttons on the current page to expand comments.
        """
        try:
            # Find all comment buttons
            comment_buttons = self.page.locator("button.show-sub-somment")
            count = comment_buttons.count()

            logger.info(f"Found {count} comment buttons on the page")

            # Click each button
            for i in range(count):
                button = comment_buttons.nth(i)
                # Get the comment count for logging
                comment_text = button.inner_text()

                logger.info(f"Clicking comment button {i+1}/{count}: {comment_text}")
                button.click()

                # Wait a bit for comments to load
                time.sleep(1)

            logger.info("Successfully clicked all comment buttons")
            return True
        except Exception as e:
            logger.error(f"Failed to click comment buttons: {e}")
            return False

    def close(self) -> None:
        """Close the browser."""
        if self.browser:
            self.browser.close()

    def run(self):
        try:
            # Setup browser and login
            self.setup_browser(headless=True)
            if not self.login():
                logger.error("Login failed. Exiting.")
                return []

            # Search for keyword
            if not self.search(self.keyword):
                logger.error("Search failed. Exiting.")
                return []

            # Get total pages and scrape each page
            total_pages = self.get_total_pages()
            logger.info(f"Found {total_pages} pages of results")

            # Calculate the page number to stop at
            until_page = total_pages - (
                total_pages if self.pages_lookback is None else self.pages_lookback
            )
            logger.info(f"Scraping from page {total_pages} to page {until_page+1}")

            posts = []
            comments = []
            for page_num in range(total_pages, until_page, -1):
                if not self.navigate_to_page(page_num):
                    logger.error(f"Failed to navigate to page {page_num}. Exiting.")
                    return []

                # Get current page URL
                current_url = self.page.url
                logger.info(f"Current page URL: {current_url}")

                # Extract posts
                logger.info(f"Extracting posts from page {page_num}")
                page_posts = self.extract_posts_on_page()
                logger.info(f"Extracted {len(page_posts)} posts from page {page_num}")

                # Add page_url to each post
                for post in page_posts:
                    post["url"] = current_url

                posts.extend(page_posts)

                # Click all comment button in page
                self.click_all_comment_buttons()
                # Wait comments to fully loaded
                # self.page.wait_for_load_state("networkidle")

                # Extract comments
                logger.info(f"Extracting comments from page {page_num}")
                page_comments = self.extract_comments_on_page()
                logger.info(
                    f"Extracted {len(page_comments)} comments from page {page_num}"
                )

                # Add page_url to each comment
                for comment in page_comments:
                    comment["url"] = current_url

                comments.extend(page_comments)

            logger.info(f"Found {len(posts)} posts in total")
            logger.info(f"Found {len(comments)} comments in total")

            return posts, comments

        except Exception as e:
            logger.error(f"Error during scraping: {e}")
        finally:
            # Clean up
            self.close()
