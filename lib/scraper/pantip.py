from playwright.sync_api import sync_playwright, <PERSON>, Locator
import re
import json
import datetime
import pendulum
from lib.common.logger import logger
import requests

from pydantic import BaseModel, Field
from typing import Literal, List, Optional


class PantipTopicMetadata(BaseModel):
    content_type: Literal["post", "mention"]
    url: str
    mention_no: Optional[str] = None


class PantipBaseModel(BaseModel):
    @classmethod
    def _parse_common(cls, data: dict):
        data["like_count"] = int(data["like_count"])
        data["emotion_count"] = int(data["emotion_count"])
        data["engagement_count"] = data["like_count"] + data["emotion_count"]
        data["timestamp"] = pendulum.from_format(
            data["timestamp"], "MM/DD/YYYY HH:mm:ss", tz="Asia/Bangkok"
        ).in_timezone("UTC")
        return data


class PantipPost(PantipBaseModel):
    id: str
    timestamp: datetime.datetime
    title: str
    content: str
    author_id: str
    author_name: str
    like_count: int = 0
    emotion_count: int = 0
    engagement_count: int = 0

    url: str

    @classmethod
    def parse(cls, data: dict):
        data = cls._parse_common(data)
        return cls.model_validate(data)


class PantipComment(PantipBaseModel):
    id: str
    timestamp: datetime.datetime
    comment_no: str
    content: str
    author_id: str
    author_name: str
    like_count: int = 0
    emotion_count: int = 0
    engagement_count: int = 0
    post_id: Optional[str] = None
    mention_id: Optional[str] = None
    parent_comment_id: Optional[str] = None
    url: str

    @classmethod
    def parse(cls, data: dict):
        data = cls._parse_common(data)
        return cls.model_validate(data)


class PantipMention(PantipBaseModel):
    id: str
    comment_no: str
    content: str
    like_count: int = 0
    emotion_count: int = 0
    engagement_count: int = 0
    author_id: str
    author_name: str
    timestamp: datetime.datetime
    url: str
    post_id: str
    post_title: str

    @classmethod
    def parse(cls, data: dict):
        data = cls._parse_common(data)
        return cls.model_validate(data)


class PantipScraper:
    BASE_URL = "https://search.pantip.com"
    SEARCH_URL = "https://search.pantip.com/ss?q={KEYWORD}"
    TOPIC_URL = "https://pantip.com/topic/{TOPIC_ID}"
    COMMENT_URL = "https://pantip.com/topic/{TOPIC_ID}/comment{COMMENT_NO}"
    NUM_TOPIC_PER_PAGE = 20

    def __init__(
        self,
    ):

        self.setup_browser(headless=True)
        self.topic_page = None

    def setup_browser(self, headless: bool = True) -> None:
        """
        Set up the browser instance.

        Args:
            headless: Whether to run browser in headless mode
        """
        playwright = sync_playwright().start()
        self.browser = playwright.chromium.launch(headless=headless)
        self.context = self.browser.new_context()
        self.page = self.context.new_page()

    def close(self) -> None:
        """Close the browser."""
        if self.browser:
            self.browser.close()

    def goto_search_page(self, keyword):
        if self.page is None:
            raise ValueError("Page not initialized")
        self.page.goto(self.SEARCH_URL.format(KEYWORD=keyword), timeout=60000)

    def goto_topic_page(self, link: str):
        self.topic_page = self.context.new_page()
        self.topic_page.goto(link, timeout=60000)

    def close_topic_page(self):
        if self.topic_page:
            self.topic_page.close()

    def get_num_topics(self) -> int:
        try:
            self.page.wait_for_selector(".ndf", state="visible", timeout=60000)
            total_topics = self.page.locator(".ndf").text_content()
            return int(total_topics)  # type: ignore
        except Exception as e:
            logger.error(f"Failed to find number of topics: {e}")
            return 0

    def get_total_pages(self):
        total_topics = self.get_num_topics()
        return round(total_topics / self.NUM_TOPIC_PER_PAGE)

    def resolve_link(self, encoded_hash) -> str:
        url = self.BASE_URL + encoded_hash

        response = requests.get(url, allow_redirects=False)
        if response.status_code == 302:
            redirect_url = response.headers.get("Location")
            if redirect_url is None:
                logger.info(f"Failed to resolve URL: {url}")
                raise ValueError(f"Failed to resolve URL: {url}")
            else:
                return redirect_url
        else:
            raise ValueError(f"Unexpected status code: {response.status_code}")

    def format_metadata(
        self, links_list: list[tuple[str, str]], keyword: str
    ) -> List[PantipTopicMetadata]:
        topic_metadata = []

        # first one is the pantip post
        topic = links_list[0]
        # extract post link
        post_text = topic[1]
        post_link = topic[0]
        post_id = post_link.split("=")[-1]

        # The topic is considered a post if either:
        # 1. It has only one link (no comments)
        # 2. The post text contains the keyword
        if len(links_list) == 1 or keyword in post_text.lower():
            post_link = self.resolve_link(post_link)
            logger.info(f"Found post: {post_link}")

            topic_metadata.append(
                PantipTopicMetadata(
                    content_type="post",
                    url=post_link,
                    mention_no=None,
                )
            )
            return topic_metadata

        # remove topic title
        links_list.pop(0)
        # extract all comments links
        comments_links = []
        for link, text in links_list:
            link = self.resolve_link(link)
            logger.info(f"Found mention: {link}")
            # Clean the text
            text = text.strip().replace("&nbsp;", "")
            comment_no = text.replace("#", "")
            topic_metadata.append(
                PantipTopicMetadata(
                    content_type="mention",
                    url=link,
                    mention_no=comment_no,
                )
            )
            comments_links.append((link, comment_no))
        return topic_metadata

    def extract_topics_metadata(self, html: str, keyword: str):
        # Split based on number prefix like "1:&nbsp;", "2:&nbsp;", etc.
        parts = re.split(r"(\d+):\s*&nbsp;?", html)
        logger.info(f"Found {len(parts)} topics")

        result = []
        for i in range(1, len(parts), 2):
            content = parts[i + 1]
            links = re.findall(r'<a[^>]*href="([^"]+)"[^>]*>(.*?)</a>', content)
            item = self.format_metadata(links, keyword)
            result.extend(item)
        return result

    def extract_search_page_content(self, page):
        # Target the specific paragraph section
        selector = "#ss-form > table > tbody > tr:nth-child(3) > td > table > tbody > tr:nth-child(2) > td:nth-child(1) > p"
        page.wait_for_selector(selector, state="visible", timeout=60000)

        # Extract raw HTML content
        html = page.locator(selector).inner_html()
        return html

    def clean_text(self, text: str) -> str | None:
        # Replace escape charater like \n,\t, etc..
        text = text.replace("\n", " ").replace("\t", " ").replace("\r", " ")
        text = text.replace("[Spoil] คลิกเพื่อดูข้อความที่ซ่อนไว้", "")

        if text == "":
            return None
        return text

    def extract_comment(self, comment: Locator) -> dict:
        comment_id = comment.get_attribute("id")
        parent_comment_id = None
        if comment_id and ("comment-" in comment_id):
            comment_id = comment_id.replace("comment-", "")

        # case reply
        if comment_id and ("reply-" in comment_id):
            data_rp = comment.get_attribute("data-rp")
            parent_comment_id, comment_id, _ = data_rp.split("_")
            # comment_id = comment_id.replace("reply-", "")

        comment_no = comment.locator(".display-post-number").text_content().strip()
        # Extract only number from text like "ความคิดเห็นที่ 42"
        comment_no = re.search(r"\d+(?:-\d+)?", comment_no).group(0)

        # Extract comment text
        comment_text = comment.locator("div.display-post-story").text_content().strip()

        # Extract like score
        like_score = comment.locator("span.like-score").text_content().strip()

        # Extract emotion score
        emotion_score = comment.locator("span.emotion-score").text_content().strip()

        # Extract author information
        author_element = comment.locator("a.display-post-name")
        author_id = None

        # Get author ID from the profile URL
        author_profile_url = author_element.get_attribute("href")
        if author_profile_url:
            # Extract ID from URL like "/profile/177254"
            author_id = author_profile_url.split("/")[-1]

        author_name = author_element.text_content().strip()

        # Extract date from the abbr element
        date_element = comment.locator("span.display-post-timestamp abbr")
        date = date_element.get_attribute("data-utime")

        # Get url

        return {
            "id": comment_id,
            "parent_comment_id": parent_comment_id,
            "comment_no": comment_no,
            "content": self.clean_text(comment_text),
            "like_count": like_score,
            "emotion_count": emotion_score,
            "author_id": author_id,
            "author_name": author_name,
            "timestamp": date,
        }

    def extract_mention(
        self, page: Page, comment_no: str
    ) -> tuple[PantipMention, List[PantipComment]]:
        """
        Extract a specific comment and its replies from a Pantip page.

        Args:
            page: The Playwright page object
            comment_no: The comment number to extract

        Returns:
            A tuple containing:
                - The mention data (dict)
                - List of comments replying to the mention (List[dict])
        """
        # Wait for the specific comment to be visible
        mention_selector = (
            f"div.display-post-wrapper:has-text('ความคิดเห็นที่ {comment_no}')"
        )
        page.wait_for_selector(mention_selector, state="visible")

        # Locate the comment container
        mention = page.locator(mention_selector).first

        mention_data = self.extract_comment(mention)  # return dict

        # Get post data
        post_data = self.extract_post_story(page)
        mention_data["post_id"] = post_data["id"]
        mention_data["post_title"] = post_data["title"]
        mention_data["url"] = page.url

        # parse to PantipMention
        mention_data = PantipMention.parse(mention_data)

        # Check if there's a comment of comment
        mention_id = mention_data.id
        comments_of_mention = self.extract_comments_of_mention(page, mention_id)
        if comments_of_mention:
            logger.info(f"Found {len(comments_of_mention)} comments of mention")
            for comment in comments_of_mention:
                comment["url"] = page.url
                comment["mention_id"] = mention_data.id

        # Parse comment data
        comments_of_mention = [
            PantipComment.parse(comment) for comment in comments_of_mention
        ]

        return mention_data, comments_of_mention

    def extract_comments_of_mention(self, page: Page, comment_id: str) -> List[dict]:
        comments = []
        elements = page.query_selector_all("[data-rp]")

        # Filter elements where the value of data-rp contains the substring
        for element in elements:
            data_rp_value = element.get_attribute("data-rp")
            if data_rp_value and comment_id in data_rp_value:
                logger.info(f"Found comments of comment id {comment_id}")
                # Convert ElementHandle to Locator before passing to extract_comment
                element_locator = page.locator(f"[data-rp='{data_rp_value}']")
                comment_data = self.extract_comment(element_locator)

                # Edit data for comments of mentionfor comments of comment, the parent_comment_id is mention_id
                comment_data["mention_id"] = comment_data["parent_comment_id"]
                comment_data["parent_comment_id"] = None
                # skip if no content
                if comment_data["content"] is None:
                    continue
                comments.append(comment_data)

        return comments

    def extract_post_story(self, page: Page):
        # Wait for the main content to load
        page.wait_for_selector("div.display-post-wrapper.main-post", state="visible")

        # Extract topic ID from URL or div id
        topic = page.locator("div.display-post-wrapper.main-post")
        topic_id = topic.get_attribute("id").replace("topic-", "")

        # Extract post title
        post_title = topic.locator("h2.display-post-title").text_content().strip()

        # Extract post text
        post_text = topic.locator("div.display-post-story").text_content().strip()

        # Extract images from the post
        image_elements = topic.locator("div.display-post-story img")
        image_urls = []

        # Get count of images
        image_count = image_elements.count()

        # Extract each image URL
        for i in range(image_count):
            img = image_elements.nth(i)
            src = img.get_attribute("src")
            if src:
                image_urls.append(src)

        # Extract post date
        post_date_element = topic.locator("span.display-post-timestamp abbr")
        post_date = post_date_element.get_attribute("data-utime")

        # Extract like score
        like_score = topic.locator("span.like-score").text_content().strip()

        # Extract emotion score
        emotion_score = topic.locator("span.emotion-score").text_content().strip()

        # Extract author ID and name
        author_element = topic.locator("a.display-post-name.owner")
        author_id = author_element.get_attribute("id")
        author_name = author_element.text_content().strip()

        # Get url
        url = page.url

        return {
            "id": topic_id,
            "title": post_title,
            "content": self.clean_text(post_text),
            "timestamp": post_date,
            "like_count": like_score,
            "emotion_count": emotion_score,
            "author_id": author_id,
            "author_name": author_name,
            "url": url,
        }

    def scrape_post(self, link: str) -> tuple[PantipPost, List[PantipComment]] | None:
        try:
            self.goto_topic_page(link)
            if self.topic_page is None:
                raise ValueError("Failed to create topic page")

            # Get post story
            post_story = self.extract_post_story(self.topic_page)
            post_data = PantipPost.parse(post_story)
            logger.info(f"Extracted post: {post_data.id}")

            # Find all comments
            comment_data = []
            comments = self.topic_page.locator(
                "div.display-post-wrapper.with-top-border.section-comment"
            ).all()
            logger.info(f"Found {len(comments)} comments")
            comment_data = [self.extract_comment(comment) for comment in comments]

            # Add url and post_id to each comment
            for comment in comment_data:
                comment["url"] = self.COMMENT_URL.format(
                    TOPIC_ID=post_data.id, COMMENT_NO=comment["comment_no"]
                )
                comment["post_id"] = post_data.id

            # Parse comment data
            comment_data = [PantipComment.parse(comment) for comment in comment_data]

            return post_data, comment_data
        except Exception as e:
            logger.error(f"Error during scraping: {e}")
        finally:
            self.close_topic_page()

    def scrape_mention(
        self, link: str, comment_no: str
    ) -> tuple[PantipMention, List[PantipComment]] | None:
        try:
            self.goto_topic_page(link)
            if self.topic_page is None:
                raise ValueError("Failed to create topic page")

            mention_data, comments_of_mention = self.extract_mention(
                self.topic_page, comment_no
            )
            if mention_data is None:
                return None
            return mention_data, comments_of_mention
        except Exception as e:
            logger.error(f"Error during scraping: {e}")
        finally:
            self.close_topic_page()

    def search(
        self, keyword: str, pages_lookback: int | None = None
    ) -> List[PantipTopicMetadata]:
        try:
            # Setup browser
            self.goto_search_page(keyword)

            # Get total pages
            total_pages = self.get_total_pages()
            logger.info(f"Found {total_pages} pages of results")

            # Set pages_lookback
            if pages_lookback is None:
                pages_lookback = total_pages

            logger.info(f"Scraping from page 1 to page {pages_lookback}")

            all_topics = []

            for page_num in range(1, pages_lookback + 1):
                logger.info(f"Scraping page {page_num}")
                self.page.wait_for_load_state("networkidle")

                html = self.extract_search_page_content(self.page)
                topics = self.extract_topics_metadata(html, keyword)
                all_topics.extend(topics)

                # Click next page if not on last page
                if page_num < pages_lookback + 1:
                    nextpage_selector = "text=หน้าถัดไป"
                    keyword_element = self.page.locator(nextpage_selector).first
                    if keyword_element:
                        keyword_element.click()
                    else:
                        logger.error("Next page element not found. Exiting.")
                        break

            return all_topics
        except Exception as e:
            logger.error(f"Error during scraping: {e}")
            return []
