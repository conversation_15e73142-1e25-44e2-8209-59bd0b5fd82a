from tvDatafeed import TvDatafeed, Interval
import pandas as pd
import json
from typing import List


class TradingViewScraper:
    def __init__(self, exchange: str = "SET", interval: Interval = Interval.in_daily):
        self.tv = TvDatafeed()
        self.exchange = exchange
        self.interval = interval

    def _fetch_stock_price(self, symbol: str, n_bars: int = 1):
        self.data = self.tv.get_hist(
            exchange=self.exchange,
            interval=self.interval,
            symbol=symbol,
            n_bars=n_bars,
        )
        return self.data

    def _convert_to_json(self, data: pd.DataFrame):
        # Data is converted to JSON format as string type
        data_converted = data.reset_index()
        data_converted = data_converted.to_json(orient="records")
        # Convert string type to list of dictionaries
        data_list = json.loads(data_converted)
        return data_list

    def run(self, symbol, n_bars: int = 1) -> List[dict]:
        data = self._fetch_stock_price(symbol=symbol, n_bars=n_bars)
        result = self._convert_to_json(data)
        return result


if __name__ == "__main__":
    scraper = TradingViewScraper(exchange="SET", interval=Interval.in_daily)
    result = scraper.run(symbol="MEDEZE", n_bars=1)
    print(result)
    print(type(result))
