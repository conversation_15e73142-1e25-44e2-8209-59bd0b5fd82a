import requests
import re
import datetime
import pendulum
import json
from typing import List
from lib.common.config import SecretConfig
from lib.common.logger import logger
from playwright.sync_api import sync_playwright

import requests
from bs4 import BeautifulSoup
import trafilatura
from requests.exceptions import RequestException
from typing import Optional
from pydantic import BaseModel, HttpUrl, Field, ValidationError


class NewsMetadata(BaseModel):
    timestamp: datetime.datetime
    title: str
    snippet: str
    url: str = Field(..., alias="link")
    author_name: Optional[str] = Field(None, alias="source")

    @classmethod
    def _format_date(cls, date_str: str) -> datetime.datetime:
        """Convert relative date strings like '2 weeks ago' to timestamp.

        Args:
            date_str: A string in the format '<number> <time_unit> ago'
                      Examples: '2 hours ago', '1 day ago', '3 months ago'

        Returns:
            datetime.datetime: Converted timestamp
        """
        # Handle exact dates like "Dec 30, 2024"
        try:
            return pendulum.parse(date_str)
        except ValueError:
            pass

        # Handle relative dates
        now = pendulum.now()

        # Extract number and unit from the string
        parts = date_str.split()
        if len(parts) < 3 or parts[-1] != "ago":
            logger.warning(f"Unrecognized date format: {date_str}")
            return now  # Return current time if format is unrecognized

        try:
            amount = int(parts[0])
            unit = parts[1].lower()

            # Handle singular/plural units
            if unit.endswith("s") and amount == 1:
                unit = unit[:-1]  # Remove trailing 's' for singular
            elif not unit.endswith("s") and amount > 1:
                unit += "s"  # Add trailing 's' for plural

            # Map to pendulum units
            unit_mapping = {
                "second": "seconds",
                "seconds": "seconds",
                "minute": "minutes",
                "minutes": "minutes",
                "hour": "hours",
                "hours": "hours",
                "day": "days",
                "days": "days",
                "week": "weeks",
                "weeks": "weeks",
                "month": "months",
                "months": "months",
                "year": "years",
                "years": "years",
            }

            if unit in unit_mapping:
                return now.subtract(**{unit_mapping[unit]: amount})
            else:
                logger.warning(f"Unknown time unit: {unit} in date: {date_str}")
                return now

        except (ValueError, IndexError) as e:
            logger.error(f"Error parsing date '{date_str}': {str(e)}")
            return now

    @classmethod
    def parse(cls, data: dict):
        # Parse and convert timestamp to datetime
        data["timestamp"] = cls._format_date(data["date"])
        return cls.model_validate(data)


class News(BaseModel):
    timestamp: datetime.datetime = Field(..., alias="timestamp")
    title: str
    snippet: str  # Field("[No snippet]", alias="snippet")
    content: Optional[str] = Field(
        "[Error: Failed to download content]", alias="content"
    )
    url: str  # = Field(..., alias="url")
    author_name: Optional[str] = None  # Field(..., alias="author_name")
    extract_method: Optional[str] = None  # = Field(..., alias="method")


class NewsScraper:
    SEARCH_URL = "https://google.serper.dev/news"

    def __init__(
        self,
        api_key: str,
        days_lookback: int = 3,
        start_date: str | None = None,
        end_date: str | None = None,
    ):
        self.api_key = api_key
        self._validate_and_setup_date(days_lookback, start_date, end_date)

    def _validate_and_setup_date(
        self, days_lookback: int, start_date: str | None, end_date: str | None
    ):
        # validate date argument
        if not days_lookback and not start_date:
            raise ValueError("Either days_lookback or start_date must be provided")

        if end_date:
            self.end_date = pendulum.parse(end_date)
        else:
            self.end_date = pendulum.now()

        if start_date:
            self.start_date = pendulum.parse(start_date)
        else:
            self.start_date = self.end_date.subtract(days=days_lookback)  # type: ignore

    def _get_query(self, keyword: str) -> str:
        # conver timestamp to date string for search
        start_date = self.start_date.to_date_string()
        end_date = self.end_date.to_date_string()
        query = f"{keyword} after:{start_date} before:{end_date}"
        logger.info(f"Search Query: {query}")
        return query

    def _search_google_news(self, keywords: List[str]) -> List[dict]:
        """Fetch news articles using the Serper API.

        Returns:
            List[dict]: List of news article data
        """
        all_news = []
        for keyword in keywords:
            logger.info(f"Fetching news for keyword: {keyword}")
            page = 1
            while True:
                try:
                    payload = json.dumps(
                        {
                            "q": self._get_query(keyword),
                            "gl": "th",
                            "num": 100,
                            "page": page,
                        }
                    )

                    headers = {
                        "X-API-KEY": self.api_key,
                        "Content-Type": "application/json",
                    }

                    logger.info(f"Sending request to {self.SEARCH_URL} (page {page})")
                    response = requests.post(
                        self.SEARCH_URL, headers=headers, data=payload, timeout=30
                    )

                    response.raise_for_status()
                    result = response.json()
                    news_items = result.get("news", [])

                    logger.info(
                        f"Retrieved {len(news_items)} news articles on page {page}"
                    )

                    # If no more results, break the loop
                    if not news_items:
                        logger.info(f"No more results for keyword: {keyword}")
                        break

                    all_news.extend(news_items)
                    page += 1

                except requests.RequestException as e:
                    logger.error(f"Error fetching news on page {page}: {str(e)}")
                    break  # Break on error to avoid infinite loop

        return all_news

    def _filter_out_of_date(
        self, news_metadata: List[NewsMetadata]
    ) -> List[NewsMetadata]:
        """Filter news articles that are out of date range."""

        filtered_news = [
            item
            for item in news_metadata
            if self.start_date <= item.timestamp <= self.end_date  # type: ignore
        ]
        logger.info(
            f"Removed {len(news_metadata) - len(filtered_news)} out of date news"
        )
        return filtered_news

    def _is_social_media(self, url: str) -> bool:
        """Check if URL is from social media platforms."""

        social_media_patterns = [
            r"facebook\.com",
            r"twitter\.com",
            r"x\.com",
            r"instagram\.com",
        ]
        return any(re.search(pattern, url.lower()) for pattern in social_media_patterns)

    def _extract_with_beautifulsoup(self, soup: BeautifulSoup) -> str:
        # Define candidate selectors
        candidates = [
            {"name": "div", "class_": "entry-content"},
            {"name": "div", "class_": "td-post-content"},
            {"name": "div", "class_": "article-content"},
            {"name": "div", "class_": "content"},
            {"name": "article", "class_": None},
            {"name": "cp-article", "class_": None},
            {"name": "div", "class_": "post-content"},
            {"name": "div", "class_": "post-body"},
            {"name": "div", "class_": "post-entry"},
            {"name": "div", "class_": "blog-post-content"},
            {"name": "div", "class_": "post-article"},
            {"name": "div", "class_": "text-content"},
            {"name": "div", "class_": "main-content"},
            {"name": "div", "class_": "news-content"},
            {"name": "div", "class_": "single-post"},
            {"name": "div", "class_": "articleBody"},
            {"name": "div", "class_": "article-body"},
            {"name": "div", "class_": "story-body"},
            {"name": "section", "class_": "article"},
            {"name": "section", "class_": "content-body"},
            {"name": "section", "class_": "main-content"},
            {"name": "div", "class_": "detail-content"},
        ]

        for candidate in candidates:
            tag = soup.find(candidate["name"], class_=candidate["class_"])
            if tag and tag.get_text(strip=True):
                return tag.get_text(strip=True)

        # Looping for all candidates, still do not have content
        result = "Failed to extract content with beautifulsoup"
        return result

    def _extract_all_paragraphs(self, soup: BeautifulSoup) -> str:
        # 4. Fallback to all paragraph tags
        paragraphs = soup.find_all("p")
        if paragraphs:
            return "\n".join(p.get_text(strip=True) for p in paragraphs)

    def _get_content_trafilatura(self, url: str) -> str:
        """Get content from URL using trafilatura."""
        downloaded = trafilatura.fetch_url(url)
        if downloaded:
            text = trafilatura.extract(downloaded)
            if text:
                return text
        else:
            return "[Error: Failed to download content]"

    def _get_content_beautifulsoup(self, url: str) -> str:
        # Define headers to mimic a browser request
        headers = {"User-Agent": "Mozilla/5.0"}

        # Request the page
        try:
            res = requests.get(url, headers=headers, timeout=10)
            if res.status_code != 200:
                return f"[Error: status {res.status_code}]"
        except RequestException as e:
            return f"[Request failed: {e}]"
        soup = BeautifulSoup(res.content, "lxml")

        # Extract with beautifulsoup
        result_extract_with_beautifulsoup = self._extract_with_beautifulsoup(soup)
        if (
            result_extract_with_beautifulsoup
            != "Failed to extract content with beautifulsoup"
            and result_extract_with_beautifulsoup != "[Error: status 403]"
        ):
            return result_extract_with_beautifulsoup

        # Fallback to all paragraph tags
        result_extract_all_paragraphs = self._extract_all_paragraphs(soup)

        if result_extract_all_paragraphs:
            return result_extract_all_paragraphs
        # JS protection detected
        elif "enable javascript" in soup.get_text(strip=True).lower():
            return "[Blocked: JavaScript required]"

        return "[Error: Failed to download content by BeautifulSoup]"

    def _get_content_playwright(self, url: str) -> str:
        try:
            with sync_playwright() as p:
                browser = p.chromium.launch(headless=True)
                page = browser.new_page()
                page.goto(url, timeout=60000)
                page.wait_for_load_state("domcontentloaded")
                content = page.content()
                soup = BeautifulSoup(content, "lxml")

            result_extract_with_beautifulsoup = self._extract_with_beautifulsoup(soup)
            if (
                result_extract_with_beautifulsoup
                != "Failed to extract content with beautifulsoup"
            ):
                return result_extract_with_beautifulsoup

            # Fallback to all paragraph tags
            result_extract_all_paragraphs = self._extract_all_paragraphs(soup)

            if result_extract_all_paragraphs:
                return result_extract_all_paragraphs

            return "[Error: Failed to download content by Playwright]"
            # browser.close()
        finally:
            pass
            # content = page.locator("body").inner_text()
            # browser.close()
            # return content

    def _get_content(self, url: str) -> tuple[str, str | None]:
        """Get content from URL."""
        # Try trafilatura
        result = self._get_content_trafilatura(url)
        if result != "[Error: Failed to download content]" and (result is not None):
            logger.info(f"Successfully extracted content from {url} with trafilatura")
            return result, "trafilatura"
        logger.info(f"Failed to extract content from {url} using trafilatura")

        # Try beautifulsoup
        result = self._get_content_beautifulsoup(url)
        if (
            result != "[Error: Failed to download content by BeautifulSoup]"
            and result != "[Blocked: JavaScript required]"
            and result != "[Error: status 403]"
        ) and (result is not None):
            logger.info(f"Successfully extracted content from {url} with beautifulsoup")
            return result, "beautifulsoup"
        logger.info(f"Failed to extract content from {url} using beautifulsoup")

        # Try playwright
        result = self._get_content_playwright(url)
        if (
            result != "[Error: Failed to download content by Playwright]"
            and result != "[Error: status 403]"
        ) and (result is not None):
            logger.info(f"Successfully extracted content from {url} with playwright")
            return result, "playwright"
        logger.info(f"Failed to extract content from {url} using playwright")

        return "[Error: Failed to download content]", None

    def _remove_duplicate(self, news: List[dict]) -> List[dict]:
        unique_news = {item["link"]: item for item in news}
        logger.info(f"Removed {len(news) - len(unique_news)} duplicate news")
        return list(unique_news.values())

    def _remove_social_media(self, news: List[dict]) -> List[dict]:
        filtered_news = [
            item for item in news if not self._is_social_media(item["link"])
        ]
        logger.info(f"Removed {len(news) - len(filtered_news)} social media news")
        return filtered_news

    def run_search(self, keyword: List[str]) -> List[dict]:
        news_metadata = self._search_google_news(keyword)

        # Clean
        news_metadata = self._remove_duplicate(news_metadata)
        news_metadata = self._remove_social_media(news_metadata)

        logger.info(f"Found {len(news_metadata)} news articles")

        # Format date before filter out date
        news_metadata = [NewsMetadata.parse(item) for item in news_metadata]

        # Filter out of date
        news_metadata = self._filter_out_of_date(news_metadata)
        return [item.model_dump() for item in news_metadata]

    def run_get_content(self, news_metadata: dict) -> dict:

        content, method = self._get_content(str(news_metadata["url"]))

        # convert news_metadata to news and add content and method
        news = news_metadata
        news["content"] = content
        news["extract_method"] = method

        # Validation
        news = News(**news)

        return news.model_dump()


# for debug
def write_json_file(data: list[dict], filename: str) -> None:
    """Write data to a JSON file."""
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=4)


if __name__ == "__main__":
    api_key = SecretConfig().get_config(["serper", "api_key"])
    keywords = ["medeze", "เมดีซ"]
    scraper = NewsScraper(api_key=api_key, days_lookback=5)
    content = scraper._get_content(
        "https://pr.moph.go.th/online/index/set/lang_thai/aUVqOG9wQ0NRMFVYVnJ1UG9UTDI1MjMyc0VXZjFLTnlhT2N2ZXRUVGlua2N6SVovT3hyaUlFWWRDeUdJU2pIMC94MkNGcytPb0s1S1lxV0RVU3lNbHJqcUlOOHJkeGZvbXdCSTdDaUgwQzQ9"
    )
    # content = scraper._get_content_trafilatura(
    #     "https://www.msn.com/en-us/money/topstocks/medeze-treasury-launches-partial-offer-for-cordlife-group-shares/ar-AA1HpxFu"
    # )

    print(content)
