import re
from typing import Optional, List


class TextCleaner:
    """Utility class for cleaning and preprocessing text content."""

    @staticmethod
    def remove_invisible_unicode(text: str) -> str:
        """Remove invisible unicode characters but keep Thai characters."""
        return re.sub(
            r"[\u200b\u200c\u200d\u200e\u200f\u202a-\u202e\u2060\uFEFF]",
            "",
            text,
        )

    @staticmethod
    def remove_hashtags(text: str, keep_hashtags: Optional[List[str]]) -> str:
        """Remove hashtags except those in keep_hashtags list."""
        pattern = re.compile(r"#(\S+)", re.IGNORECASE)

        def repl(m):
            tag = m.group(1)
            if any(tag.lower() == keep_tag.lower() for keep_tag in keep_hashtags):
                return m.group(0)  # keep the hashtag as is
            else:
                return ""  # remove other hashtags

        return pattern.sub(repl, text)

    @staticmethod
    def remove_links(text: str) -> str:
        """Remove HTTP/HTTPS links from text."""
        return re.sub(r"http\S+", "", text)

    @staticmethod
    def remove_extra_spaces(text: str) -> str:
        """Remove extra whitespace and strip text."""
        return re.sub(r"\s+", " ", text).strip()
