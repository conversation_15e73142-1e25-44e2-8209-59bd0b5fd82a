import os
from pathlib import Path
from typing import Union, Optional
from yaml import safe_load, YAMLError
from lib.common.logger import logger

from lib.storage.database import DBConfig

# from lib.storage.redis import RedisConfig
# from lib.integration.x import XConfig

SECRET_CONFIG_PATH = os.path.join(os.getcwd(), "config.yaml")

# Config object to manage config
# from_dict
# from_path


class Config:
    def __init__(self, path: str):
        self._configs = self._read_config(path)

    def _read_config(self, path) -> dict:
        # validate file
        if self._is_valid_file(path):
            # read config file
            with open(path, "r", encoding="utf-8") as stream:
                try:
                    return safe_load(stream)
                except YAMLError as e:
                    logger.error(e)
                    raise e
        return dict()

    def _is_valid_file(self, s: str) -> bool:
        """Check if a given string is a valid file path."""
        path = Path(s)

        # Check if the file has a valid YAML extension
        if path.suffix.lower() not in {".yaml", ".yml"}:
            raise Exception("Config file is not yaml file")

        # verify that the file exists
        if not path.exists():
            raise FileNotFoundError

        return True

    def get_config(self, keys: Optional[str | list] = None) -> dict | str:
        if isinstance(keys, str):
            try:
                config = self._configs[keys]
            except KeyError as e:
                raise e
        elif isinstance(keys, list):
            config = self._configs
            try:
                for key in keys:
                    config = config[key]
            except KeyError as e:
                raise e
        else:
            config = self._configs

        return config

    def find_key(self, key: str, var: Optional[dict] = None):
        if not var:
            var = self._configs

        if key in var:
            return var[key]

        for value in var.values():
            if isinstance(value, dict):
                result = self.find_key(key, value)  # Recursively search in nested dicts
                if result is not None:
                    return result  # Return the found value
            elif isinstance(value, list):  # Handle lists containing dictionaries
                for item in value:
                    if isinstance(item, dict):
                        result = self.find_key(key, item)
                        if result is not None:
                            return result  # Return if found in list

        return None  # Return None if the key is not found anywhere


class SecretConfig(Config):
    def __init__(self, path: str = SECRET_CONFIG_PATH):
        super().__init__(path)

    def get_db_config(self) -> DBConfig:
        config = self.get_config("database")
        if not isinstance(config, dict):
            raise ValueError("Database config must be a dictionary")
        return DBConfig.model_validate(config)

    def get_apify_token(self) -> str:
        token = self.get_config(["apify", "token"])
        if not isinstance(token, str):
            raise ValueError("Apify token must be a string")
        return token

    def get_datalake_bucket(self) -> str:
        bucket = self.get_config(["datalake-bucket", "name"])
        if not isinstance(bucket, str):
            raise ValueError("Datalake bucket name must be a string")
        return bucket

    def get_datalake_bucket_credentials_path(self) -> str:
        path = self.get_config(["datalake-bucket", "credentials_path"])
        if not isinstance(path, str):
            raise ValueError("Datalake bucket credentials path must be a string")
        return path

    def get_llm_service_url(self) -> str:
        url = self.get_config(["llm-service", "url"])
        if not isinstance(url, str):
            raise ValueError("LLM service url must be a string")
        return url


#     def get_openai_token(self) -> str:
#         token = self.get_config(["openai", "token"])
#         return token  # type: ignore

#     def get_x_config(self) -> XConfig:
#         config = self.get_config("x_api_ai_agent")
#         return XConfig.model_validate(config)

#     def get_serper_token(self) -> str:
#         token = self.get_config(["serper", "api_key"])
#         return token  # type: ignore

#     def get_redis_config(self) -> RedisConfig:
#         config = self.get_config("redis")
#         return RedisConfig.model_validate(config)

#     def get_x_username(self) -> str:
#         username = self.get_config(["x_api_ai_agent", "username"])
#         return username  # type: ignore

#     def get_time_window(self) -> int:
#         time_window = self.get_config(["x_api_ai_agent", "time_window"])
#         return time_window  # type: ignore
