from jobs.base_job import BaseJob
from lib.common.config import SecretConfig
from lib.storage.database import Database, DBConfig
from lib.llm_service import LLMService, LLMServiceError
from lib.common.logger import logger
import json
import asyncio
from typing import List
from pydantic import BaseModel


class Feedback(BaseModel):
    topic: str
    details: List[str]

    @classmethod
    def model_validate(cls, data: dict):
        data["details"] = data["content"]
        return super().model_validate(data)


class DashboardCurrentSentiment(BaseModel):
    detail: List[str]


class DashboardHighlightJob(BaseJob):
    SOURCE_TABLE = ["feedback_investor_sentiment", "feedback_consumer_sentiment"]
    SENTIMENT_TYPE_ID_LIST = [1, 2]
    TARGET_TABLE = "dashboard_current_sentiment"
    ENDPOINT = "/api/dashboard/current-sentiment"

    def __init__(
        self,
        llm_service_url: str = SecretConfig().get_llm_service_url(),
        db_config: DBConfig = SecretConfig().get_db_config(),
    ):
        self.db = Database(db_config)
        self.llm_service = LLMService(llm_service_url)

    def _run_sql(self, table_name: str, sentiment_type_id: int) -> List[dict]:
        sql = f"""
        SELECT info
        FROM {table_name}
        WHERE sentiment_type = {sentiment_type_id}
        ORDER BY created_at DESC
        LIMIT 1
        """
        logger.info(f"Run sql for {table_name} with sentiment type {sentiment_type_id}")
        result = self.db.execute(sql)
        info = result[0]["info"]

        return info

    def _get_feedback_data(self) -> List[Feedback]:
        feedback = []
        for table_name in self.SOURCE_TABLE:
            for sentiment_type_id in self.SENTIMENT_TYPE_ID_LIST:
                result = self._run_sql(table_name, sentiment_type_id)
                if result:
                    # loop items and filter only topic and content field
                    for item in result:
                        feedback.append(Feedback.model_validate(item))

        return feedback

    async def _get_highlight(self, data: List[Feedback]) -> List[str]:
        # convert data to json payload
        payload = [item.model_dump() for item in data]
        print(payload)
        try:
            response = await self.llm_service.post(
                endpoint=self.ENDPOINT, data=json.dumps(payload)
            )
            if response is None:
                logger.info(f"Response is None")
                return []
            logger.info(f"Response: {response}")
            return response["highlights"]
        except LLMServiceError as e:
            logger.error(f"LLM service error: {e} ")
            return []

    def _load(self, data: List[str]):
        insert_data = DashboardCurrentSentiment(detail=data)
        # insert to database
        self.db.insert(self.TARGET_TABLE, insert_data.model_dump(mode="json"))
        logger.info(f"Inserted data to {self.TARGET_TABLE}")

    async def run(self):
        data = self._get_feedback_data()
        logger.info(f"Found {len(data)} feedback data")

        highlight = await self._get_highlight(data)
        # load to database
        self._load(highlight)


if __name__ == "__main__":
    logger.info("Starting the job...")
    job = DashboardHighlightJob()
    asyncio.run(job.run())
    logger.info("Job completed successfully.")
