import asyncio

import datetime
from pydantic import BaseModel
from lib.storage.database import Database, DBConfig
from lib.common.config import SecretConfig
from lib.common.logger import logger
from lib.llm_service import LLMService, LLMServiceError
import json
from typing import List
from jobs.base_job import BaseJob


class TopicContent(BaseModel):
    topic: str
    content: List[str]
    count_comment: int
    percent_comment: float

    @classmethod
    def model_validate(cls, data: dict, total_count: int):
        data["content"] = data["summary"]
        data["count_comment"] = data["topic_count"]
        if total_count == 0:
            data["percent_comment"] = 0
        else:
            data["percent_comment"] = round(
                data["count_comment"] * 100 / total_count, 2
            )
        return super().model_validate(data)


class Topic(BaseModel):
    sentiment_type: int
    info: List[TopicContent]


class Post(BaseModel):
    id: str
    timestamp: datetime.datetime
    content: str
    sentiment: str
    topics: List[str]


class Comment(BaseModel):
    id: str
    timestamp: datetime.datetime
    content: str
    sentiment: str
    post_id: str
    topics: List[str]


class FeedbackPostRequest(BaseModel):
    type: str = "post_with_comments"
    post: str
    comments: List[str] = []


class FeedbackChatRequest(BaseModel):
    type: str = "chat_conversation"
    messages: List[str]


class FeedbackRequest(BaseModel):
    payload: List[FeedbackPostRequest | FeedbackChatRequest]


class FeedbackJob(BaseJob):
    SOURCE_TABLE = "dashboard_report_data"
    AUTHOR_TYPE_LIST = ["investor", "consumer"]
    SENTIMENT_TYPE_LIST = ["positive", "negative"]
    SENTIMENT_TYPE_ID_LIST = [1, 2]
    TARGET_TABLE = "feedback_{author_type}_sentiment"

    ENDPOINT = "/api/feedback"

    def __init__(
        self,
        days_lookback: int = 14,
        llm_service_url: str = SecretConfig().get_llm_service_url(),
        db_config: DBConfig = SecretConfig().get_db_config(),
    ):
        self.db = Database(db_config)
        self.days_lookback = days_lookback
        self.llm_service = LLMService(llm_service_url)

    def _get_data(self, content_type: str, author_type: str):

        sql = f"""
            SELECT id,
                timestamp,
                content,
                metadata->>'post_id' as post_id,
                metadata->>'conversation_id' as conversation_id,
                metadata->>'content_order' as content_order,
                sentiment_type_id as sentiment,
                topics
            FROM {self.SOURCE_TABLE}
            WHERE (timestamp >= (now() - interval '{self.days_lookback} days')) and sentiment_type_id in (1,2,3) and content_type = '{content_type}' and author_type = '{author_type}'
        """
        # WHERE (timestamp BETWEEN '2025-06-28' AND '2025-07-18')
        results = self.db.execute(query=sql)
        logger.info(f"Found {len(results)} {content_type} results for {author_type}")
        return results

    def _load(self, data: Topic, author_type):
        table_name = self.TARGET_TABLE.format(author_type=author_type)
        # Convert to dict and ensure JSON serialization
        data_dict = data.model_dump(mode="json")
        data_dict["info"] = json.dumps(data_dict["info"])
        self.db.insert(table_name, [data_dict])
        logger.info(f"Inserted data to {table_name}")

    def _prepare_post_with_comments(
        self, post_data: List[dict], comment_data: List[dict], sentiment_type_id: int
    ) -> List[FeedbackPostRequest]:
        sentiment_posts = [
            post for post in post_data if post["sentiment"] == sentiment_type_id
        ]
        sentiment_comments = [
            comment
            for comment in comment_data
            if comment["sentiment"] == sentiment_type_id
        ]
        # count
        sentiment_post_count = len(sentiment_posts)
        sentiment_comment_count = len(sentiment_comments)
        sentiment_total_count = sentiment_post_count + sentiment_comment_count
        logger.info(
            f"Found {sentiment_post_count} posts and {sentiment_comment_count} comments ({sentiment_total_count} total) with {sentiment_type_id} sentiment"
        )
        result = []
        for post in sentiment_posts:
            post_comments = [
                comment["content"]
                for comment in sentiment_comments
                if comment.get("post_id") == post["id"]
            ]

            post_item = {
                "post": post["content"],
                "comments": post_comments,
            }
            result.append(FeedbackPostRequest.model_validate(post_item))

        return result

    def _prepare_chat(
        self, chat_data: List[dict], sentiment_type_id: int
    ) -> List[FeedbackChatRequest]:
        grouped_data = {}
        for item in chat_data:
            conversation_id = item.get("conversation_id")
            if conversation_id and item["sentiment"] == sentiment_type_id:
                if conversation_id not in grouped_data:
                    grouped_data[conversation_id] = {
                        "conversation_id": conversation_id,
                        "messages": [],
                    }
                grouped_data[conversation_id]["messages"].append(
                    {
                        "content": item["content"],
                        "content_order": (
                            int(item["content_order"]) if item["content_order"] else 0
                        ),
                    }
                )

        # Sort messages by content_order for each conversation
        for conversation_id in grouped_data:
            grouped_data[conversation_id]["messages"].sort(
                key=lambda x: x["content_order"]
            )
            # Extract only content after sorting
            grouped_data[conversation_id]["messages"] = [
                msg["content"] for msg in grouped_data[conversation_id]["messages"]
            ]

        result = [
            FeedbackChatRequest.model_validate(item) for item in grouped_data.values()
        ]
        return result

    def _process_result(
        self, data: List[dict], total_count: int, sentiment_type_id: int
    ) -> Topic:
        # convert each to TopicContent
        topic_content_list = []
        for item in data:
            topic_content_list.append(TopicContent.model_validate(item, total_count))
        # convert to Topic
        topic = Topic(sentiment_type=sentiment_type_id, info=topic_content_list)
        return topic

    async def _get_feedback(self, request_data: FeedbackRequest) -> List[dict]:
        payload = request_data.payload
        if payload:
            # Convert payload to list of dictionaries
            payload_dicts = [item.model_dump() for item in payload]
            # send to llm service
            response = await self.llm_service.post(
                endpoint=self.ENDPOINT, data=json.dumps(payload_dicts)
            )
            if response is None:
                logger.info(f"Response is None")
                return []
            logger.info(f"Response: {response}")
            return response["content"]
        else:
            logger.info(f"No payload")
            return []

    # for debug
    def write_json(self, data: list[dict], filename: str) -> None:
        with open(filename, "w") as f:
            json.dump(data, f, indent=2, default=str, ensure_ascii=False)

    async def run(self):
        async with self.llm_service:

            for author_type in self.AUTHOR_TYPE_LIST:

                posts = self._get_data("post", author_type)
                comments = self._get_data("comment", author_type)
                chat = self._get_data("chat", author_type)

                # Count posts and comments
                post_count = len(posts)
                comment_count = len(comments)
                chat_count = len(chat)
                total_count = post_count + comment_count + chat_count

                logger.info(
                    f"Found {post_count} posts and {comment_count} comments {chat_count} chat ({total_count} total) for {author_type}"
                )

                for sentiment in self.SENTIMENT_TYPE_LIST:
                    logger.info(
                        f"=====Processing {author_type} with sentiment {sentiment}====="
                    )

                    sentiment_type_id = self.SENTIMENT_TYPE_ID_LIST[
                        self.SENTIMENT_TYPE_LIST.index(sentiment)
                    ]

                    post_with_comments = self._prepare_post_with_comments(
                        posts, comments, sentiment_type_id
                    )

                    chat_conversation = self._prepare_chat(chat, sentiment_type_id)

                    # make payload
                    payload = FeedbackRequest(
                        payload=[*post_with_comments, *chat_conversation]
                    )

                    # call LLM
                    result = await self._get_feedback(payload)

                    # process result
                    topic = self._process_result(result, total_count, sentiment_type_id)

                    # write to db
                    self._load(topic, author_type)


if __name__ == "__main__":
    logger.info("Starting the job...")
    job = FeedbackJob()
    asyncio.run(job.run())
    logger.info("Job completed successfully.")
