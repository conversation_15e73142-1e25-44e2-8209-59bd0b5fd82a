from jobs.base_job import TransformJob, RAW_DATA_PATH, JOB_CONFIG_PATH
from lib.common.config import SecretConfig
from lib.common.logger import logger
from lib.scraper.stock_price import TradingViewScraper
from lib.storage.database import Database
from typing import List
import datetime
from pydantic import BaseModel
import pendulum


class StockPrice(BaseModel):
    date: datetime.datetime
    open: float
    high: float
    low: float
    close: float

    @classmethod
    def parse(cls, data: dict):
        # Parse and convert timestamp to datetime and truncate to date
        data["date"] = pendulum.from_timestamp(data["datetime"] / 1000).date()
        return cls.model_validate(data)


class StockPriceVolume(StockPrice):
    volume: float


class StockPriceJob(TransformJob):
    STOCK_MAPPING = {
        "MEDEZE": "quant_medeze_price",
        "SET": "quant_set_price",
        "HELTH": "quant_helth_price",
    }

    def __init__(self):
        self.scraper = TradingViewScraper()
        self.db = Database(SecretConfig().get_db_config())

    def extract(self, symbol: str, n_bars: int = 1) -> List[dict]:
        return self.scraper.run(symbol=symbol, n_bars=n_bars)

    def transform(self, data):
        # If symbol is MEDEZE, drop only symbol column
        return [
            (
                StockPriceVolume.parse(item)
                if item["symbol"] == "SET:MEDEZE"
                else StockPrice.parse(item)
            )
            for item in data
        ]

    def load(self, data: List[StockPriceVolume | StockPrice], table_name: str):
        logger.info(f"Upserting data: {data}")
        self.db.upsert(
            table=table_name,
            data=[item.model_dump() for item in data],
            unique_columns=["date"],
        )  # Unsure unique_columns

    def run(self, n_bars: int = 1):
        for symbol, table_name in self.STOCK_MAPPING.items():
            data = self.extract(symbol=symbol, n_bars=n_bars)
            transformed_data = self.transform(data)
            self.load(transformed_data, table_name)


if __name__ == "__main__":
    logger.info("Starting the job...")
    job = StockPriceJob()
    # default nbars = 1
    job.run()
    logger.info("Job completed successfully.")
