from lib.common.logger import logger
from lib.scraper.price_performance import MedezePricePerformanceScraper
from lib.storage.database import Database
from lib.common.config import SecretConfig
from jobs.base_job import TransformJob
from pydantic import BaseModel


class QuantPricePerformance(BaseModel):
    ytd_medeze: float
    ytd_helth: float
    ytd_set: float
    pe_medeze: float
    pe_helth: float
    pe_set: float


class QuantPricePerformanceJob(TransformJob):
    def __init__(self):
        self.scraper = MedezePricePerformanceScraper()
        self.db = Database(SecretConfig().get_db_config())

    def extract(self):
        return self.scraper.run()

    def filter(self, data: dict):
        required_keys = ["YTD", "P/E (X)"]
        missing_keys = [key for key in required_keys if key not in data]

        if missing_keys:
            raise ValueError(
                f"Missing required keys in data: {', '.join(missing_keys)}"
            )

        filtered_data = {}
        for key in required_keys:
            filtered_data[key] = data[key]
        return filtered_data

    def transform(self, data: dict):
        data = self.filter(data)
        ytd_medeze = data["YTD"]["medeze"]
        ytd_helth = data["YTD"]["helth"]
        ytd_set = data["YTD"]["set"]
        pe_medeze = data["P/E (X)"]["medeze"]
        pe_helth = data["P/E (X)"]["helth"]
        pe_set = data["P/E (X)"]["set"]
        return QuantPricePerformance(
            ytd_medeze=ytd_medeze,
            ytd_helth=ytd_helth,
            ytd_set=ytd_set,
            pe_medeze=pe_medeze,
            pe_helth=pe_helth,
            pe_set=pe_set,
        )

    def load(self, data: QuantPricePerformance):
        logger.info(f"Inserting data: {data}")
        self.db.insert("quant_price_performance", data.model_dump())

    def run(self):
        data = self.extract()
        transformed_data = self.transform(data)
        self.load(transformed_data)


if __name__ == "__main__":
    logger.info("Starting the job...")
    job = QuantPricePerformanceJob()
    job.run()
    logger.info("Job completed successfully.")
