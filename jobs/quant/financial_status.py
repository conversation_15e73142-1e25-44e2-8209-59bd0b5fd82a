from jobs.base_job import <PERSON>Job
from pydantic import BaseModel
from typing import Literal
from lib.common.config import SecretConfig
from lib.storage.database import Database, DBConfig
from lib.common.logger import logger


# 1 - no concern
# 2 - concern
class QuantFinancialStatus(BaseModel):
    revenue: Literal[1, 2]
    margin: Literal[1, 2]
    net_income: Literal[1, 2]


class QuantFinancialSentimentJob(BaseJob):
    METRICS = ["Margin", "Revenue", "Net income"]
    TARGET_TABLE = "quant_financial_sentiment_status"

    SQL = """
        SELECT count(*)
        FROM dashboard_report_data
        WHERE '{metric}' = ANY(topics)
        AND "timestamp" >= NOW() - INTERVAL '{days_lookback} days'
        AND sentiment_type_id = 2;
    """
    # 2 is negative sentiment

    def __init__(
        self,
        days_lookback: int = 14,
        db_config: DBConfig = SecretConfig().get_db_config(),
    ):
        self.days_lookback = days_lookback
        self.db = Database(db_config)

    def _is_metric_concern(self, metric: str, days_lookback: int) -> bool:
        """Find if the specific metric has negative sentiment in the last days_lookback days.

        Args:
            metric (str): metric to check
            days_lookback (int): days to lookback

        Returns:
            bool: true is concern, false is no concern
        """
        sql = self.SQL.format(metric=metric, days_lookback=days_lookback)
        result = self.db.execute(sql)
        count = result[0]["count"]
        return count > 0

    def _load(self, data: QuantFinancialStatus):
        self.db.insert(self.TARGET_TABLE, data.model_dump())

    def run(self):
        result = {}
        for metric in self.METRICS:
            # 1 - no concern
            # 2 - concern
            key = metric.lower().replace(" ", "_")
            result[key] = (
                2 if self._is_metric_concern(metric, self.days_lookback) else 1
            )
        logger.info(f"Result: {result}")
        self._load(QuantFinancialStatus(**result))


if __name__ == "__main__":
    logger.info("Starting the job...")
    job = QuantFinancialSentimentJob()
    job.run()
    logger.info("Job completed successfully.")
