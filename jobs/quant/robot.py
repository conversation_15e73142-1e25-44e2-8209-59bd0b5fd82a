import json
from playwright.sync_api import sync_playwright
from playwright.async_api import async_playwright
import base64
import asyncio
import uuid

from jobs.base_job import BaseJob
from lib.common.logger import logger
from pydantic import BaseModel
from lib.common.config import SecretConfig
from lib.storage.database import Database, DBConfig
from lib.llm_service import LLMService, LLMServiceError


class StockPrice(BaseModel):
    id: str
    support_value: float
    resistance_value: float
    support_text: str
    resistance_text: str

    @classmethod
    def parse(cls, data: dict):
        # Parse and create uuid data
        data["id"] = str(uuid.uuid4())
        return cls.model_validate(data)


class PriceTrend(BaseModel):
    long_term_trend: str
    short_term_trend: str


class QuantRobot(BaseJob):
    MEDEZE_TRADING_VIEW_URL = "https://www.tradingview.com/chart/?symbol=SET%3AMEDEZE"
    CHART_ANALYSIS_ENDPOINT = "/api/quant-robot"
    ROBOT_TABLE = "quant_robot"
    PRICE_TREND_TABLE = "quant_price_trend"

    def __init__(
        self,
        db_config: DBConfig = SecretConfig().get_db_config(),
        llm_service_url: str = SecretConfig().get_llm_service_url(),
    ):
        self.db = Database(db_config)
        self.llm_service = LLMService(llm_service_url)

    async def screenshot_tradingview(self, url: str = MEDEZE_TRADING_VIEW_URL) -> str:
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            await page.goto(url, timeout=100000)

            # Click "Watchlist" button by aria-label
            await page.locator(
                'button[aria-label="Watchlist, details and news"]'
            ).click()

            await asyncio.sleep(1)

            # Try to click "1Y" button first, fall back to "All" if not found
            try:
                # Look for "1Y" button with timeout
                one_year_button = await page.locator(
                    "div.js-button-text.text-GwQQdU8S", has_text="1Y"
                ).first.wait_for(timeout=3000)
                await one_year_button.click()
                logger.info("Clicked 1Y button")
            except Exception as e:
                logger.info(f"1Y button not found, falling back to All: {e}")
                # Fall back to "All" button
                await page.locator(
                    "div.js-button-text.text-GwQQdU8S", has_text="All"
                ).click()
                logger.info("Clicked All button")

            await asyncio.sleep(3)

            # Take screenshot bytes
            image_bytes = await page.screenshot(full_page=True)

            await browser.close()

        image_base64 = base64.b64encode(image_bytes).decode("utf-8")
        return image_base64

    async def trading_chart_analysis(self, image_base_64: str) -> dict | None:
        # call llm service
        try:
            payload = {"image_base64": image_base_64}
            json_payload = json.dumps(payload, ensure_ascii=False)

            if not json_payload:
                logger.info(f"Payload is None for {image_base_64}")
                return None
            response = await self.llm_service.post(
                endpoint=self.CHART_ANALYSIS_ENDPOINT, data=json_payload
            )
            if response is None:
                return None
            return response
        except LLMServiceError as e:
            logger.error(f"LLM service error: {e} for {image_base_64}")
            return None

    def transform_price(self, analyzed_data: dict) -> StockPrice:
        result = {}
        # Split support and resistance
        support_parts = analyzed_data["support"].split(" - ", 1)
        result["support_value"] = float(support_parts[0])
        result["support_text"] = support_parts[1] if len(support_parts) > 1 else None

        resistance_parts = analyzed_data["resistance"].split(" - ", 1)
        result["resistance_value"] = float(resistance_parts[0])
        result["resistance_text"] = (
            resistance_parts[1] if len(resistance_parts) > 1 else None
        )

        return StockPrice.parse(result)

    def transform_trend(self, analyzed_data: dict) -> PriceTrend:
        return PriceTrend(
            long_term_trend=analyzed_data["trend"]["long_term"],
            short_term_trend=analyzed_data["trend"]["short_term"],
        )

    def load_price(self, data: StockPrice) -> None:
        self.db.insert(self.ROBOT_TABLE, [data.model_dump()])
        logger.info(f"Inserted price data")

    def load_trend(self, data: PriceTrend) -> None:
        self.db.insert(self.PRICE_TREND_TABLE, [data.model_dump()])
        logger.info(f"Inserted trend data")

    async def run(self) -> None:
        image_base64 = await self.screenshot_tradingview()
        chart_analysis_result = await self.trading_chart_analysis(image_base64)
        if chart_analysis_result is None:
            logger.info("No chart analysis result")
            return
        price_data = self.transform_price(chart_analysis_result)
        trend_data = self.transform_trend(chart_analysis_result)
        self.load_price(price_data)
        self.load_trend(trend_data)


if __name__ == "__main__":
    job = QuantRobot()
    asyncio.run(job.run())
