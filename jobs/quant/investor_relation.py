from jobs.base_job import BaseJob
from lib.storage.database import Database, DBConfig
from lib.common.config import SecretConfig
from lib.llm_service import LLMService, LLMServiceError
from typing import List, Dict
from lib.common.logger import logger
import json
import asyncio
import re
from pydantic import BaseModel


class RecommendedPlan(BaseModel):
    topic: str
    detail: List[str]


class QuantSentimentPlan(BaseModel):
    current_detail: List[str]
    recommended_plan: List[RecommendedPlan]

    @classmethod
    def parse(cls, data: dict):
        data["current_detail"] = data["concerns"]
        data["recommended_plan"] = data["plans"]
        return cls.model_validate(data)


class QuantInvestorRelationJob(BaseJob):
    TARGET_TABLE = "quant_sentiment_plan"
    ENDPOINT = "/api/investor-relation"

    def __init__(
        self,
        days_lookback: int = 14,
        llm_service_url: str = SecretConfig().get_llm_service_url(),
        db_config: DBConfig = SecretConfig().get_db_config(),
    ):
        self.days_lookback = days_lookback
        self.llm_service = LLMService(llm_service_url)
        self.db = Database(db_config)

    def _get_data(self) -> List[dict]:
        sql = f"""
        with comment_data as (
        select c.id, c.content as comment, c.metadata->>'post_id' as post_id, topics, c.sentiment_type_id
        from dashboard_report_data c
        WHERE (c.timestamp >= (now() - interval '{self.days_lookback} days')) and c.content_type = 'comment' and ('Unrelated'!= ANY(c.topics) and c.sentiment_type_id != 4)
        ),
        post_data as (
        select p.id, p.timestamp, p.content as post, p.sentiment_type_id
        from dashboard_report_data p
        where (p.timestamp >= (now() - interval '{self.days_lookback} days')) and p.author_type = 'investor' and p.content_type = 'post' and ('Unrelated'!= ANY(p.topics) and p.sentiment_type_id != 4)
        )
        select p.id,p.post, array_agg(c.comment) as comments
        from post_data p
        left join comment_data c
        on p.id = c.post_id
        where p.sentiment_type_id =2 or c.sentiment_type_id =2
        group by p.post,p.id
        """
        results = self.db.execute(query=sql)
        return results

    def _prepare_payload(self, data: List[dict]) -> List[str]:
        payload = []

        for row in data:
            post = self._clean_invisible_characters(row["post"])
            row_comments = row["comments"]
            if row_comments:
                comments = [
                    self._clean_invisible_characters(c)
                    for c in row_comments
                    if c is not None
                ]
            else:
                comments = []
            item = {"post": post, "comments": comments}
            payload.append(item)

        return payload

    async def _call_llm(self, payload: List[str]):
        # send to llm service
        try:
            response = await self.llm_service.post(
                endpoint=self.ENDPOINT, data=json.dumps(payload)
            )
            if response is None:
                logger.info("Response is None")
                return
            logger.info(f"Response: {response}")
        except LLMServiceError as e:
            logger.error(f"LLM service error: {e}")
            return
        return response

    def _load(self, data: QuantSentimentPlan):
        value = data.model_dump(mode="json")

        # Convert dict/list fields to JSON strings for JSONB columns
        if "recommended_plan" in value:
            value["recommended_plan"] = json.dumps(value["recommended_plan"])

        self.db.insert(self.TARGET_TABLE, value)
        logger.info(f"Inserted data to {self.TARGET_TABLE}")

    def _clean_invisible_characters(self, text: str) -> str:
        """Remove all invisible unicode characters including control chars and formatting marks."""

        return re.sub(
            r"[\u0000-\u001F\u007F\u00A0\u180E\u200B-\u200F\u2028-\u2029\u2060-\u2063\uFEFF]",
            "",
            text,
        )

    async def run(self):
        # get negative sentiment data
        data = self._get_data()

        # prepare payload for llm
        payload = self._prepare_payload(data)

        # call llm service
        response = await self._call_llm(payload)
        if response is None:
            logger.info("Response is None")
            return

        # parse response
        parsed_response = QuantSentimentPlan.parse(response)

        # load to table
        self._load(parsed_response)


if __name__ == "__main__":
    logger.info("Starting the job...")
    job = QuantInvestorRelationJob(days_lookback=30)
    asyncio.run(job.run())
    logger.info("Job completed successfully.")
