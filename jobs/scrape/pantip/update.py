from typing import List
from lib.scraper.pantip import PantipTopicMetadata
from lib.common.logger import logger

from jobs.scrape.pantip.base_pantip import PantipJob


class PantipUpdateJob(PantipJob):

    def __init__(self):
        super().__init__()
        self.days_lookback = self._get_pantip_update_config()

    def _get_pantip_update_config(self) -> int:
        pantip_update_config = self.job_config.get_config(["pantip", "update"])
        # validate config - ensure it's a dict with proper types
        if not isinstance(pantip_update_config, dict):
            raise ValueError("pantip update config must be a dictionary")
        return pantip_update_config["days_lookback"]

    def _get_posts_to_update(self) -> List[dict]:
        query = f"""
        select
            url
        from
            {self.POST_TABLE}
        where
            timestamp >= (now() - interval '{self.days_lookback} days')
        """
        return self.db.execute(query)

    def _get_mentions_to_update(self) -> List[dict]:
        query = f"""
        select
            url,
            comment_no
        from
            {self.MENTION_TABLE}
        where
            timestamp >= (now() - interval '{self.days_lookback} days')
        """
        return self.db.execute(query)

    def run(self):

        # update post
        posts = self._get_posts_to_update()
        if posts:
            logger.info(f"Found {len(posts)} posts to update")
            for post in posts:
                self._process_post(post["url"])

        # update mention
        mentions = self._get_mentions_to_update()
        if mentions:
            logger.info(f"Found {len(mentions)} mentions to update")
            for mention in mentions:
                self._process_mention(mention["url"], mention["comment_no"])

        logger.info(f"Scraped {self.post_count} posts")
        logger.info(f"Scraped {self.mention_count} mentions")
        logger.info(f"Scraped {self.comment_count} comments")


if __name__ == "__main__":
    logger.info("Starting the job...")
    job = PantipUpdateJob()
    job.run()
    logger.info("Job completed successfully.")
