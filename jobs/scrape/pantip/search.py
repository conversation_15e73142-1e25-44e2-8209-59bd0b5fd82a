from lib.scraper.pantip import (
    PantipScraper,
    PantipTopicMetadata,
    PantipPost,
    PantipComment,
    PantipMention,
)
from jobs.scrape.pantip.base_pantip import PantipJob
from jobs.base_job import BaseJob, JOB_CONFIG_PATH
from lib.storage.database import Database, DBConfig
from lib.common.config import SecretConfig
from lib.common.config import Config

from typing import List
from lib.common.logger import logger
import json


class PantipSearchJob(PantipJob):

    def __init__(
        self,
        job_config_path: str = JOB_CONFIG_PATH,
        db_config: DBConfig = SecretConfig().get_db_config(),
    ):
        super().__init__(job_config_path, db_config)
        self.keyword, self.pages_lookback = self._get_pantip_search_config()

    def _get_pantip_search_config(self) -> tuple[str, int]:
        pantip_search_config = self.job_config.get_config(["pantip", "search"])
        # validate config - ensure it's a dict with proper types
        if not isinstance(pantip_search_config, dict):
            raise ValueError("pantip search config must be a dictionary")
        return pantip_search_config["keyword"], pantip_search_config["pages_lookback"]

    def _search(self) -> List[dict]:
        topics_metadata = self.scraper.search(self.keyword, self.pages_lookback)
        if topics_metadata:
            topics_metadata_json = [item.model_dump() for item in topics_metadata]
            logger.info(f"Found {len(topics_metadata_json)} topics")
            return topics_metadata_json
        else:
            logger.info("No topics found")
            return []

    def _fetch_exitsting_topics(self) -> List[dict]:
        metadata = self.db.read(self.METADATA_TABLE)
        if metadata:
            return metadata
        else:
            return []

    def _get_new_topics(
        self, fetch_topics: List[dict], existing_topics: List[dict]
    ) -> List[dict]:

        existing_topic = [item["url"] for item in existing_topics]
        new_topics = [
            item for item in fetch_topics if item["url"] not in existing_topic
        ]
        logger.info(f"Found {len(new_topics)} new topics")
        return new_topics

    def _save_metadata(self, new_topics: List[PantipTopicMetadata]) -> int:
        # convert pydantic model to dict
        new_topics_dict = [item.model_dump(mode="json") for item in new_topics]
        if new_topics_dict:
            self.db.insert(self.METADATA_TABLE, new_topics_dict)
            logger.info(f"Saved {len(new_topics)} metadata to database")
            return len(new_topics_dict)
        else:
            logger.info("No metadata to save")
            return 0

    def run(self):
        topics = self._search()
        existing_topics = self._fetch_exitsting_topics()
        new_topics = self._get_new_topics(topics, existing_topics)

        if new_topics:
            # Scrape new topics
            for topic in new_topics:
                # fetch post or mention
                topic_metadata = PantipTopicMetadata(**topic)
                url = topic_metadata.url
                if topic_metadata.content_type == "post":
                    self._process_post(url)
                else:
                    comment_no = topic_metadata.mention_no
                    if comment_no is not None:
                        self._process_mention(url, comment_no)
                    else:
                        raise ValueError(f"Missing comment_no for mention: {url}")

                # save metadata
                self._save_metadata([topic_metadata])

            logger.info(f"Scraped {self.post_count} posts")
            logger.info(f"Scraped {self.mention_count} mentions")
            logger.info(f"Scraped {self.comment_count} comments")


if __name__ == "__main__":
    logger.info("Starting the job...")
    job = PantipSearchJob()
    job.run()
    logger.info("Job completed successfully.")
