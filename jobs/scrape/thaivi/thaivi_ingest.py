import json

from lib.common.logger import logger
from lib.scraper.thaivi import ThaiViScraper
from lib.storage.database import Database
from lib.common.config import SecretConfig, Config
from jobs.base_job import IngestJob, RAW_DATA_PATH, JOB_CONFIG_PATH
from lib.storage.gcs import GCS
import pendulum


class ThaiViIngestJob(IngestJob):
    CHANNEL_NAME = "thaivi"

    def __init__(
        self,
        username: str,
        password: str,
        job_config_path: str = JOB_CONFIG_PATH,
        gcs_credentials_path: str = SecretConfig().get_datalake_bucket_credentials_path(),
        gcs_bucket: str = SecretConfig().get_datalake_bucket(),
    ):
        self.gcs = GCS(credentials_path=gcs_credentials_path, bucket_name=gcs_bucket)
        self.db = Database(config=SecretConfig().get_db_config())

        self.keyword, self.pages_lookback = self.get_thaivi_config(job_config_path)
        self.scraper = ThaiViScraper(
            username=username,
            password=password,
            keyword=self.keyword,
            pages_lookback=self.pages_lookback,
        )

    def get_thaivi_config(self, path: str) -> tuple[str, int | None]:
        config = Config(path)
        job_config = config.get_config([self.CHANNEL_NAME, "ingest_config"])
        logger.info(f"Loaded job config for {self.CHANNEL_NAME}")
        keyword = job_config["keyword"] if isinstance(job_config, dict) else None
        pages_lookback = (
            job_config.get("pages_lookback") if isinstance(job_config, dict) else None
        )

        if keyword is None:
            raise ValueError("Keyword must be specified in job config")

        return keyword, pages_lookback

    def extract(self):
        posts, comments = self.scraper.run()
        return posts, comments

    def load(self, post_data, comment_data):
        """Save data to GCS bucket with proper path structure."""
        ingest_date = pendulum.now().format("YYYY-MM-DD")
        path = RAW_DATA_PATH.format(
            channel_name=self.CHANNEL_NAME, ingest_date=ingest_date
        )
        post_path = f"{path}/posts.json"
        comment_path = f"{path}/comments.json"

        # Upload to GCS
        self.gcs.upload_json_file(data=post_data, file_path=post_path)
        self.gcs.upload_json_file(data=comment_data, file_path=comment_path)

    def run(self):
        posts, comments = self.extract()
        self.load(posts, comments)


if __name__ == "__main__":
    logger.info("Starting the job...")
    logger.info("Run thaivi ingest job")
    secret_config = SecretConfig()
    username = secret_config.get_config(["thaivi", "username"])
    password = secret_config.get_config(["thaivi", "password"])
    job = ThaiViIngestJob(username=str(username), password=str(password))
    job.run()
    logger.info("Job completed successfully.")
