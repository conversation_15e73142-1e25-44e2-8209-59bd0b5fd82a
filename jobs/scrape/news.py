from jobs.base_job import BaseJob, JOB_CONFIG_PATH
from lib.scraper.news import NewsScraper
from lib.storage.database import Database
from lib.common.config import SecretConfig, Config
from lib.common.logger import logger
from typing import List


class NewsJob(BaseJob):
    KEYWORD_TABLE = "data_scrape_keywords"

    def __init__(self, api_key: str, job_config_path: str = JOB_CONFIG_PATH):

        self.api_key = api_key
        self.job_config = Config(job_config_path)

        self.days_lookback, self.target_table = self._get_config()
        self.scraper = NewsScraper(
            api_key=self.api_key, days_lookback=self.days_lookback
        )
        self.db = Database(SecretConfig().get_db_config())

    def _get_config(self) -> tuple[int, str]:
        days_lookback = self.job_config.get_config(
            ["news", "ingest_config", "days_lookback"]
        )
        if not isinstance(days_lookback, int):
            raise ValueError("days_lookback must be an integer")
        target_table = self.job_config.get_config(
            ["news", "ingest_config", "target_table"]
        )
        if not isinstance(target_table, str):
            raise ValueError("target_table must be a string")
        return days_lookback, target_table

    def _fetch_keywords(self) -> list[str]:
        sql = f"""
            SELECT keyword
            FROM {self.KEYWORD_TABLE}
        """
        results = self.db.execute(query=sql)
        keywords = self._handle_multiple_keywords([row["keyword"] for row in results])
        return keywords

    def _handle_multiple_keywords(self, keywords: list[str]) -> list[str]:
        # replace + with space
        return [keyword.replace("+", " ") for keyword in keywords]

    def _fetch_exitsting_news_url(self) -> List[str]:
        sql = f"""
                SELECT url
                FROM {self.target_table}
            """
        existing_news = self.db.execute(query=sql)
        if existing_news:
            return [item["url"] for item in existing_news]
        else:
            return []

    def _get_new_news(
        self, fetch_news: List[dict], existing_news_url: List[str]
    ) -> List[dict]:

        new_news = [item for item in fetch_news if item["url"] not in existing_news_url]
        logger.info(f"Found {len(new_news)} new news")
        return new_news

    def _save_to_database(self, news: dict):
        # Upsert data into database
        rows_affected = self.db.upsert(
            table=self.target_table, data=news, unique_columns=["url"]
        )
        logger.info(
            f"Transformed data saved to table: {self.target_table} ({rows_affected} rows affected)"
        )

    def run(self):
        # fetch keyword from database
        keywords = self._fetch_keywords()

        # keywords = ["medeze", "เมดีซ"]

        # fetch new news
        news_metadata = self.scraper.run_search(keyword=keywords)
        # new_news_metadata = news_metadata

        # fetch existing news from database
        existing_news_url = self._fetch_exitsting_news_url()

        # get new news
        new_news_metadata = self._get_new_news(news_metadata, existing_news_url)

        fail_count = 0
        for item in new_news_metadata:
            try:
                news = self.scraper.run_get_content(item)
            except Exception as e:
                logger.error(f"Failed to get content: {e}")
                fail_count += 1
                continue
            news = self.scraper.run_get_content(item)
            self._save_to_database(news)

        logger.info(f"Failed to get content: {fail_count}")
        logger.info(f"Successfully get content: {len(new_news_metadata) - fail_count}")

        # throw error if fail_count > 0
        if fail_count > 0:
            raise Exception(f"Failed to get content: {fail_count}")


if __name__ == "__main__":
    api_key = SecretConfig().get_config(["serper", "api_key"])

    # check if api_key is str
    if not isinstance(api_key, str):
        raise ValueError("api_key must be a string")

    job = NewsJob(api_key=api_key)
    job.run()
